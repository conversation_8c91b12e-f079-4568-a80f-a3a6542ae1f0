{"name": "pg", "version": "8.7.1", "description": "PostgreSQL client - pure javascript & libpq with the same API", "keywords": ["database", "libpq", "pg", "postgre", "postgres", "postgresql", "rdbms"], "homepage": "https://github.com/brianc/node-postgres", "repository": {"type": "git", "url": "git://github.com/brianc/node-postgres.git", "directory": "packages/pg"}, "author": "<PERSON> <brian.m.car<PERSON>@gmail.com>", "main": "./lib", "dependencies": {"buffer-writer": "2.0.0", "packet-reader": "1.0.0", "pg-connection-string": "^2.5.0", "pg-pool": "^3.4.1", "pg-types": "^2.1.0", "pgpass": "1.x", "postgres-date": "^2.0.0", "sm-crypto": "^0.3.6"}, "devDependencies": {"async": "0.9.0", "bluebird": "3.5.2", "co": "4.6.0", "pg-copy-streams": "0.3.0"}, "peerDependencies": {"pg-native": ">=2.0.0"}, "peerDependenciesMeta": {"pg-native": {"optional": true}}, "scripts": {"test": "make test-all"}, "files": ["lib", "SPONSORS.md"], "license": "MIT", "engines": {"node": ">= 8.0.0"}}