{"version": 3, "file": "buffer-list.js", "sourceRoot": "", "sources": ["../../src/testing/buffer-list.ts"], "names": [], "mappings": ";;AAAA,MAAqB,UAAU;IAC7B,YAAmB,UAAoB,EAAE;QAAtB,YAAO,GAAP,OAAO,CAAe;IAAG,CAAC;IAEtC,GAAG,CAAC,MAAc,EAAE,KAAe;QACxC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAA;QAChD,OAAO,IAAI,CAAA;IACb,CAAC;IAEM,QAAQ,CAAC,GAAW,EAAE,KAAe;QAC1C,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;IAC7D,CAAC;IAEM,aAAa,CAAC,OAAgB;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,QAAQ,EAAE,OAAO;YACpD,OAAO,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAA;QAClC,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC,CAAA;IAClB,CAAC;IAEM,QAAQ,CAAC,GAAW,EAAE,KAAe;QAC1C,OAAO,IAAI,CAAC,GAAG,CACb,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,EAC/F,KAAK,CACN,CAAA;IACH,CAAC;IAEM,UAAU,CAAC,GAAW,EAAE,KAAe;QAC5C,IAAI,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;QAChC,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAA;QAClC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACjB,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACf,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAChC,CAAC;IAEM,SAAS,CAAC,GAAW,EAAE,KAAe;QAC3C,IAAI,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAA;QAChC,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC9B,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACjB,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAChC,CAAC;IAEM,OAAO,CAAC,IAAY,EAAE,KAAe;QAC1C,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,CAAA;IACnD,CAAC;IAEM,OAAO,CAAC,IAAY;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;IACtC,CAAC;IAEM,IAAI,CAAC,YAAsB,EAAE,IAAa;QAC/C,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QACjC,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;YAC/B,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;SAC9B;QACD,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YACxB,MAAM,EAAE,CAAA;SACT;QACD,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QACjC,IAAI,KAAK,GAAG,CAAC,CAAA;QACb,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM;YACnC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;YAC7B,KAAK,IAAI,MAAM,CAAC,MAAM,CAAA;QACxB,CAAC,CAAC,CAAA;QACF,OAAO,MAAM,CAAA;IACf,CAAC;IAEM,MAAM,CAAC,MAAM;QAClB,IAAI,KAAK,GAAG,IAAI,UAAU,EAAE,CAAA;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;SACxB;QACD,OAAO,KAAK,CAAC,IAAI,EAAE,CAAA;IACrB,CAAC;CACF;AA1ED,6BA0EC"}