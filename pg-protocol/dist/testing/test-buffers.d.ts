/// <reference types="node" />
import BufferList from './buffer-list';
declare const buffers: {
    readyForQuery: () => Buffer;
    authenticationOk: () => Buffer;
    authenticationCleartextPassword: () => Buffer;
    authenticationMD5Password: () => Buffer;
    authenticationSASL: () => Buffer;
    authenticationSASLContinue: () => Buffer;
    authenticationSASLFinal: () => Buffer;
    authenticationSM3: () => Buffer;
    parameterStatus: (name: string, value: string) => Buffer;
    backendKeyData: (processID: number, secretKey: number) => Buffer;
    commandComplete: (string: string) => Buffer;
    rowDescription: (fields: any[]) => Buffer;
    parameterDescription: (dataTypeIDs: number[]) => Buffer;
    dataRow: (columns: any[]) => Buffer;
    error: (fields: any) => Buffer;
    notice: (fields: any) => Buffer;
    errorOrNotice: (fields: any) => BufferList;
    parseComplete: () => Buffer;
    bindComplete: () => Buffer;
    notification: (id: number, channel: string, payload: string) => Buffer;
    emptyQuery: () => Buffer;
    portalSuspended: () => Buffer;
    closeComplete: () => Buffer;
    copyIn: (cols: number) => Buffer;
    copyOut: (cols: number) => Buffer;
    copyData: (bytes: Buffer) => Buffer;
    copyDone: () => Buffer;
};
export default buffers;
