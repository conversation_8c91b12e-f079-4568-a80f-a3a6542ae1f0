{"version": 3, "file": "test-buffers.js", "sourceRoot": "", "sources": ["../../src/testing/test-buffers.ts"], "names": [], "mappings": ";;;;;AAAA,gFAAgF;AAChF,gEAAsC;AAEtC,MAAM,OAAO,GAAG;IACd,aAAa,EAAE;QACb,OAAO,IAAI,qBAAU,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAC/D,CAAC;IAED,gBAAgB,EAAE;QAChB,OAAO,IAAI,qBAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACrD,CAAC;IAED,+BAA+B,EAAE;QAC/B,OAAO,IAAI,qBAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACrD,CAAC;IAED,yBAAyB,EAAE;QACzB,OAAO,IAAI,qBAAU,EAAE;aACpB,QAAQ,CAAC,CAAC,CAAC;aACX,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aAC9B,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACpB,CAAC;IAED,kBAAkB,EAAE;QAClB,OAAO,IAAI,qBAAU,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACjG,CAAC;IAED,0BAA0B,EAAE;QAC1B,OAAO,IAAI,qBAAU,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACxE,CAAC;IAED,uBAAuB,EAAE;QACvB,OAAO,IAAI,qBAAU,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACxE,CAAC;IAED,iBAAiB,EAAE;QACjB,OAAO,IAAI,qBAAU,EAAE;aACpB,QAAQ,CAAC,EAAE,CAAC;aACZ,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;aAC9B,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACpB,CAAC;IAED,eAAe,EAAE,UAAU,IAAY,EAAE,KAAa;QACpD,OAAO,IAAI,qBAAU,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAC5E,CAAC;IAED,cAAc,EAAE,UAAU,SAAiB,EAAE,SAAiB;QAC5D,OAAO,IAAI,qBAAU,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACjF,CAAC;IAED,eAAe,EAAE,UAAU,MAAc;QACvC,OAAO,IAAI,qBAAU,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAC5D,CAAC;IAED,cAAc,EAAE,UAAU,MAAa;QACrC,MAAM,GAAG,MAAM,IAAI,EAAE,CAAA;QACrB,IAAI,GAAG,GAAG,IAAI,qBAAU,EAAE,CAAA;QAC1B,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAC3B,MAAM,CAAC,OAAO,CAAC,UAAU,KAAK;YAC5B,GAAG;iBACA,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;iBACtB,QAAQ,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,CAAC;iBAC5B,QAAQ,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,CAAC;iBACpC,QAAQ,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC;iBAC/B,QAAQ,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,CAAC;iBACjC,QAAQ,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,CAAC;iBACjC,QAAQ,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC,CAAA;QACpC,CAAC,CAAC,CAAA;QACF,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAC5B,CAAC;IAED,oBAAoB,EAAE,UAAU,WAAqB;QACnD,WAAW,GAAG,WAAW,IAAI,EAAE,CAAA;QAC/B,IAAI,GAAG,GAAG,IAAI,qBAAU,EAAE,CAAA;QAC1B,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;QAChC,WAAW,CAAC,OAAO,CAAC,UAAU,UAAU;YACtC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;QAC1B,CAAC,CAAC,CAAA;QACF,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAC5B,CAAC;IAED,OAAO,EAAE,UAAU,OAAc;QAC/B,OAAO,GAAG,OAAO,IAAI,EAAE,CAAA;QACvB,IAAI,GAAG,GAAG,IAAI,qBAAU,EAAE,CAAA;QAC1B,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAC5B,OAAO,CAAC,OAAO,CAAC,UAAU,GAAG;YAC3B,IAAI,GAAG,IAAI,IAAI,EAAE;gBACf,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;aACjB;iBAAM;gBACL,IAAI,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;gBACrC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;gBAC3B,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;aAChB;QACH,CAAC,CAAC,CAAA;QACF,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAC5B,CAAC;IAED,KAAK,EAAE,UAAU,MAAW;QAC1B,OAAO,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACtD,CAAC;IAED,MAAM,EAAE,UAAU,MAAW;QAC3B,OAAO,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACtD,CAAC;IAED,aAAa,EAAE,UAAU,MAAW;QAClC,MAAM,GAAG,MAAM,IAAI,EAAE,CAAA;QACrB,IAAI,GAAG,GAAG,IAAI,qBAAU,EAAE,CAAA;QAC1B,MAAM,CAAC,OAAO,CAAC,UAAU,KAAU;YACjC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YACvB,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAC7B,CAAC,CAAC,CAAA;QACF,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA,CAAC,aAAa;IAChD,CAAC;IAED,aAAa,EAAE;QACb,OAAO,IAAI,qBAAU,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACzC,CAAC;IAED,YAAY,EAAE;QACZ,OAAO,IAAI,qBAAU,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACzC,CAAC;IAED,YAAY,EAAE,UAAU,EAAU,EAAE,OAAe,EAAE,OAAe;QAClE,OAAO,IAAI,qBAAU,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAC9F,CAAC;IAED,UAAU,EAAE;QACV,OAAO,IAAI,qBAAU,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACzC,CAAC;IAED,eAAe,EAAE;QACf,OAAO,IAAI,qBAAU,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACzC,CAAC;IAED,aAAa,EAAE;QACb,OAAO,IAAI,qBAAU,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACzC,CAAC;IAED,MAAM,EAAE,UAAU,IAAY;QAC5B,MAAM,IAAI,GAAG,IAAI,qBAAU,EAAE;YAC3B,YAAY;aACX,OAAO,CAAC,CAAC,CAAC;YACX,eAAe;aACd,QAAQ,CAAC,IAAI,CAAC,CAAA;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAC7B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;SACjB;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAC7B,CAAC;IAED,OAAO,EAAE,UAAU,IAAY;QAC7B,MAAM,IAAI,GAAG,IAAI,qBAAU,EAAE;YAC3B,YAAY;aACX,OAAO,CAAC,CAAC,CAAC;YACX,eAAe;aACd,QAAQ,CAAC,IAAI,CAAC,CAAA;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;YAC7B,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;SACjB;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IAC7B,CAAC;IAED,QAAQ,EAAE,UAAU,KAAa;QAC/B,OAAO,IAAI,qBAAU,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACpD,CAAC;IAED,QAAQ,EAAE;QACR,OAAO,IAAI,qBAAU,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACzC,CAAC;CACF,CAAA;AAED,kBAAe,OAAO,CAAA"}