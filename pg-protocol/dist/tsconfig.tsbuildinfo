{"program": {"fileInfos": {"../../../node_modules/typescript/lib/lib.es6.d.ts": {"version": "721cec59c3fef87aaf480047d821fb758b3ec9482c4129a54631e6e25e432a31", "signature": "721cec59c3fef87aaf480047d821fb758b3ec9482c4129a54631e6e25e432a31", "affectsGlobalScope": false}, "../../../node_modules/typescript/lib/lib.es5.d.ts": {"version": "c9a1f03d6ba0fe3c871eb0dd81622e78fbb61ade70878b34d48a341a690c59e9", "signature": "c9a1f03d6ba0fe3c871eb0dd81622e78fbb61ade70878b34d48a341a690c59e9", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2015.d.ts": {"version": "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "signature": "dc47c4fa66b9b9890cf076304de2a9c5201e94b740cffdf09f87296d877d71f6", "affectsGlobalScope": false}, "../../../node_modules/typescript/lib/lib.es2016.d.ts": {"version": "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "signature": "7a387c58583dfca701b6c85e0adaf43fb17d590fb16d5b2dc0a2fbd89f35c467", "affectsGlobalScope": false}, "../../../node_modules/typescript/lib/lib.es2017.d.ts": {"version": "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "signature": "8a12173c586e95f4433e0c6dc446bc88346be73ffe9ca6eec7aa63c8f3dca7f9", "affectsGlobalScope": false}, "../../../node_modules/typescript/lib/lib.es2018.d.ts": {"version": "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", "signature": "5f4e733ced4e129482ae2186aae29fde948ab7182844c3a5a51dd346182c7b06", "affectsGlobalScope": false}, "../../../node_modules/typescript/lib/lib.dom.d.ts": {"version": "38130cdd16bd2318b9362f9d60dd9670f7e38708bb6131cf11fc78a41b2c34a0", "signature": "38130cdd16bd2318b9362f9d60dd9670f7e38708bb6131cf11fc78a41b2c34a0", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.dom.iterable.d.ts": {"version": "fb0c09b697dc42afa84d1587e3c994a2f554d2a45635e4f0618768d16a86b69a", "signature": "fb0c09b697dc42afa84d1587e3c994a2f554d2a45635e4f0618768d16a86b69a", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.webworker.importscripts.d.ts": {"version": "7fac8cb5fc820bc2a59ae11ef1c5b38d3832c6d0dfaec5acdb5569137d09a481", "signature": "7fac8cb5fc820bc2a59ae11ef1c5b38d3832c6d0dfaec5acdb5569137d09a481", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.scripthost.d.ts": {"version": "097a57355ded99c68e6df1b738990448e0bf170e606707df5a7c0481ff2427cd", "signature": "097a57355ded99c68e6df1b738990448e0bf170e606707df5a7c0481ff2427cd", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2015.core.d.ts": {"version": "46ee15e9fefa913333b61eaf6b18885900b139867d89832a515059b62cf16a17", "signature": "46ee15e9fefa913333b61eaf6b18885900b139867d89832a515059b62cf16a17", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2015.collection.d.ts": {"version": "43fb1d932e4966a39a41b464a12a81899d9ae5f2c829063f5571b6b87e6d2f9c", "signature": "43fb1d932e4966a39a41b464a12a81899d9ae5f2c829063f5571b6b87e6d2f9c", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2015.generator.d.ts": {"version": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "signature": "cdccba9a388c2ee3fd6ad4018c640a471a6c060e96f1232062223063b0a5ac6a", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2015.iterable.d.ts": {"version": "42f5e41e5893da663dbf0394268f54f1da4b43dc0ddd2ea4bf471fe5361d6faf", "signature": "42f5e41e5893da663dbf0394268f54f1da4b43dc0ddd2ea4bf471fe5361d6faf", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2015.promise.d.ts": {"version": "0b7a905675e6cb4211c128f0a3aa47d414b275180a299a9aad5d3ec298abbfc4", "signature": "0b7a905675e6cb4211c128f0a3aa47d414b275180a299a9aad5d3ec298abbfc4", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2015.proxy.d.ts": {"version": "dfff68b3c34338f6b307a25d4566de15eed7973b0dc5d69f9fde2bcac1c25315", "signature": "dfff68b3c34338f6b307a25d4566de15eed7973b0dc5d69f9fde2bcac1c25315", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2015.reflect.d.ts": {"version": "cb609802a8698aa28b9c56331d4b53f590ca3c1c3a255350304ae3d06017779d", "signature": "cb609802a8698aa28b9c56331d4b53f590ca3c1c3a255350304ae3d06017779d", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2015.symbol.d.ts": {"version": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "signature": "3013574108c36fd3aaca79764002b3717da09725a36a6fc02eac386593110f93", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts": {"version": "4670208dd7da9d6c774ab1b75c1527a810388c7989c4905de6aaea8561cb9dce", "signature": "4670208dd7da9d6c774ab1b75c1527a810388c7989c4905de6aaea8561cb9dce", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2016.array.include.d.ts": {"version": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "signature": "3be5a1453daa63e031d266bf342f3943603873d890ab8b9ada95e22389389006", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2017.object.d.ts": {"version": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "signature": "17bb1fc99591b00515502d264fa55dc8370c45c5298f4a5c2083557dccba5a2a", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts": {"version": "b5e4c2d67aa844ed503b29cd4ca2ede1a229ac7fe874613b2c996fa9c581a25f", "signature": "b5e4c2d67aa844ed503b29cd4ca2ede1a229ac7fe874613b2c996fa9c581a25f", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2017.string.d.ts": {"version": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "signature": "6a6b173e739a6a99629a8594bfb294cc7329bfb7b227f12e1f7c11bc163b8577", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2017.intl.d.ts": {"version": "12a310447c5d23c7d0d5ca2af606e3bd08afda69100166730ab92c62999ebb9d", "signature": "12a310447c5d23c7d0d5ca2af606e3bd08afda69100166730ab92c62999ebb9d", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts": {"version": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "signature": "b0124885ef82641903d232172577f2ceb5d3e60aed4da1153bab4221e1f6dd4e", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts": {"version": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "signature": "0eb85d6c590b0d577919a79e0084fa1744c1beba6fd0d4e951432fa1ede5510a", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts": {"version": "a40c4d82bf13fcded295ac29f354eb7d40249613c15e07b53f2fc75e45e16359", "signature": "a40c4d82bf13fcded295ac29f354eb7d40249613c15e07b53f2fc75e45e16359", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2018.intl.d.ts": {"version": "df9c8a72ca8b0ed62f5470b41208a0587f0f73f0a7db28e5a1272cf92537518e", "signature": "df9c8a72ca8b0ed62f5470b41208a0587f0f73f0a7db28e5a1272cf92537518e", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2018.promise.d.ts": {"version": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "signature": "bb2d3fb05a1d2ffbca947cc7cbc95d23e1d053d6595391bd325deb265a18d36c", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2018.regexp.d.ts": {"version": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "signature": "c80df75850fea5caa2afe43b9949338ce4e2de086f91713e9af1a06f973872b8", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.es2020.bigint.d.ts": {"version": "7b5a10e3c897fabece5a51aa85b4111727d7adb53c2734b5d37230ff96802a09", "signature": "7b5a10e3c897fabece5a51aa85b4111727d7adb53c2734b5d37230ff96802a09", "affectsGlobalScope": true}, "../../../node_modules/typescript/lib/lib.esnext.intl.d.ts": {"version": "89bf2b7a601b73ea4311eda9c41f86a58994fec1bee3b87c4a14d68d9adcdcbd", "signature": "89bf2b7a601b73ea4311eda9c41f86a58994fec1bee3b87c4a14d68d9adcdcbd", "affectsGlobalScope": true}, "../src/buffer-writer.ts": {"version": "efacb8d05bf895d40086efc820b05072fd5c3f3d0b5059b1acde0201adf41204", "signature": "d1a87c97a5f84b8e4c0ee006fc64703a43bc9dd51c77e8082211324e11fadde2", "affectsGlobalScope": false}, "../src/messages.ts": {"version": "8027f30cb8c6c5393cc07ed58fe342d6a06ec315f16f9e5b59b85f64ff3db90a", "signature": "6b67207702e9644c4a481d1563c540a07b05ca12b1575b9a64b924aa28b48320", "affectsGlobalScope": false}, "../src/serializer.ts": {"version": "eb38816ca12db30f639316a01167d4eb509fab458b4103b943bb40b6ed49c1c8", "signature": "5a1d6d88904814997f7d83c92947b2166ef27276918a46689ac40c055694932f", "affectsGlobalScope": false}, "../src/buffer-reader.ts": {"version": "92964c612ff788811239e1fd78cde7ecda6e5e998ed476473a8a40896e76a618", "signature": "faf3b6d3fe1cc502404774e0d52048333597dd3436de55576a74ed9ded6e513f", "affectsGlobalScope": false}, "../src/parser.ts": {"version": "6475d5eedada2c88689733996c4e234af441bd72205b96f66b71175502af73eb", "signature": "6ba5cb2196c66d233e5bb5838225a9659040d88f43a4458c8e5e53c556347d64", "affectsGlobalScope": false}, "../src/index.ts": {"version": "8056a3d98516e8a1a98389b19d3f1394efebbdd8f25e9b5863da5f8501a7ece9", "signature": "e84777aa4463aec895bd1b4b1f4c8d9f04bde004f21f7c78d68a0444120f22a6", "affectsGlobalScope": false}, "../src/b.ts": {"version": "3bc372466081dbdb9f97c0c2b3be22aa53adb513b9da90d658f9d3804f6a1085", "signature": "f761c91419d0a89422a0004ef1a92929dd4d2d5e5c16758654d8b0467d1998c6", "affectsGlobalScope": false}, "../src/testing/buffer-list.ts": {"version": "dbb86acde427518fd2ccf2f84baa96439f67c67fcd9146ab0999388a6a68989e", "signature": "8d79f6b9420b139a8dcba2db3a379104019c6227d4ad2e857157465fab5b0336", "affectsGlobalScope": false}, "../src/testing/test-buffers.ts": {"version": "98ee5f1bf3b2e9b9cafa9bfb32a0337a5c19651374bf50ce3de53263b24dbdfa", "signature": "c6fd2306abb7a1ea908d65310a2322dd8300fc0c12b892ae257dba0549b79a8c", "affectsGlobalScope": false}, "../src/inbound-parser.test.ts": {"version": "af37880fd58a15019a9a3832ecd2adac8d9af4f6a4b20d7864a8edad8a9636f1", "signature": "f761c91419d0a89422a0004ef1a92929dd4d2d5e5c16758654d8b0467d1998c6", "affectsGlobalScope": false}, "../src/outbound-serializer.test.ts": {"version": "7c558b91d51454997d7329cd3cdad91036cf69e81e57c7f5a379dbffd175a5b4", "signature": "f761c91419d0a89422a0004ef1a92929dd4d2d5e5c16758654d8b0467d1998c6", "affectsGlobalScope": false}, "../src/types/chunky.d.ts": {"version": "46e42164cac01e04949a7de9a1b95e8651be09e29dfbf265ffef9b8a7808c488", "signature": "46e42164cac01e04949a7de9a1b95e8651be09e29dfbf265ffef9b8a7808c488", "affectsGlobalScope": false}, "../node_modules/@types/node/globals.d.ts": {"version": "fdfa133a1388cd095c2972b1c21fb5855e5cf2972c629ebf1f87cf3e91c52fbf", "signature": "fdfa133a1388cd095c2972b1c21fb5855e5cf2972c629ebf1f87cf3e91c52fbf", "affectsGlobalScope": true}, "../node_modules/@types/node/async_hooks.d.ts": {"version": "96e547b51f95ee76bdb25731c92420fa6f93b59c3f38f23d505be36e2de394ee", "signature": "96e547b51f95ee76bdb25731c92420fa6f93b59c3f38f23d505be36e2de394ee", "affectsGlobalScope": false}, "../node_modules/@types/node/buffer.d.ts": {"version": "61215c1a376bbe8f51cab4cc4ddbf3746387015113c37a84d981d4738c21b878", "signature": "61215c1a376bbe8f51cab4cc4ddbf3746387015113c37a84d981d4738c21b878", "affectsGlobalScope": false}, "../node_modules/@types/node/child_process.d.ts": {"version": "c2c50c74b88b0e28315d4b2fe4b05012e2786688a6c9824d3aee33118783c70a", "signature": "c2c50c74b88b0e28315d4b2fe4b05012e2786688a6c9824d3aee33118783c70a", "affectsGlobalScope": false}, "../node_modules/@types/node/cluster.d.ts": {"version": "ce629710e5e58724902b753212e97861fd73e2aa09f5d88cb6d55dc763cf8c8a", "signature": "ce629710e5e58724902b753212e97861fd73e2aa09f5d88cb6d55dc763cf8c8a", "affectsGlobalScope": false}, "../node_modules/@types/node/console.d.ts": {"version": "525c8fc510d9632d2a0a9de2d41c3ac1cdd79ff44d3b45c6d81cacabb683528d", "signature": "525c8fc510d9632d2a0a9de2d41c3ac1cdd79ff44d3b45c6d81cacabb683528d", "affectsGlobalScope": false}, "../node_modules/@types/node/constants.d.ts": {"version": "0279383034fae92db8097d0a41350293553599cc9c3c917b60e2542d0dfcbd44", "signature": "0279383034fae92db8097d0a41350293553599cc9c3c917b60e2542d0dfcbd44", "affectsGlobalScope": false}, "../node_modules/@types/node/crypto.d.ts": {"version": "5ac4813d7654d8e500befd3c4a90daf39b05304e0e606d54c02c5ed42a95fd95", "signature": "5ac4813d7654d8e500befd3c4a90daf39b05304e0e606d54c02c5ed42a95fd95", "affectsGlobalScope": false}, "../node_modules/@types/node/dgram.d.ts": {"version": "387656ed4d6444031a0042c38701167e77ff5f4698ada32737082fbee76b1db0", "signature": "387656ed4d6444031a0042c38701167e77ff5f4698ada32737082fbee76b1db0", "affectsGlobalScope": false}, "../node_modules/@types/node/dns.d.ts": {"version": "ef226a42de7022eacdfa0f15aabf73b46c47af93044c8ebfab8aa8e3cf6c330c", "signature": "ef226a42de7022eacdfa0f15aabf73b46c47af93044c8ebfab8aa8e3cf6c330c", "affectsGlobalScope": false}, "../node_modules/@types/node/domain.d.ts": {"version": "d5b7c8819ce1bd31a45f7675309e145ec28e3aa1b60a8e0637fd0e8916255baa", "signature": "d5b7c8819ce1bd31a45f7675309e145ec28e3aa1b60a8e0637fd0e8916255baa", "affectsGlobalScope": false}, "../node_modules/@types/node/events.d.ts": {"version": "76048f3c7325a6c1fa6306d40eb0c8570fa0209d09472d46f9b1221f66edae6f", "signature": "76048f3c7325a6c1fa6306d40eb0c8570fa0209d09472d46f9b1221f66edae6f", "affectsGlobalScope": false}, "../node_modules/@types/node/fs.d.ts": {"version": "61ee8bf16e7f4ea94e1db482973b128f1cd6b4ab0c9bef793e5cac382ad65f22", "signature": "61ee8bf16e7f4ea94e1db482973b128f1cd6b4ab0c9bef793e5cac382ad65f22", "affectsGlobalScope": false}, "../node_modules/@types/node/http.d.ts": {"version": "ad573a6d6b035f4361907c2cb006e9c67ae26c86413527254a1b2c83ed79455d", "signature": "ad573a6d6b035f4361907c2cb006e9c67ae26c86413527254a1b2c83ed79455d", "affectsGlobalScope": false}, "../node_modules/@types/node/http2.d.ts": {"version": "272c8598c3a29a3fa3027bd0a645c5f49b3f7832dfcf8e47b7260843ec8a40f3", "signature": "272c8598c3a29a3fa3027bd0a645c5f49b3f7832dfcf8e47b7260843ec8a40f3", "affectsGlobalScope": false}, "../node_modules/@types/node/https.d.ts": {"version": "dacbe08610729f6343ea9880ea8e737c6d7a6efa4a318d8f6acaf85db4aceed6", "signature": "dacbe08610729f6343ea9880ea8e737c6d7a6efa4a318d8f6acaf85db4aceed6", "affectsGlobalScope": false}, "../node_modules/@types/node/inspector.d.ts": {"version": "4218ced3933a31eed1278d350dd63c5900df0f0904f57d61c054d7a4b83dbe4c", "signature": "4218ced3933a31eed1278d350dd63c5900df0f0904f57d61c054d7a4b83dbe4c", "affectsGlobalScope": false}, "../node_modules/@types/node/module.d.ts": {"version": "03394bf8deb8781b490ae9266a843fbdf00647947d79e25fcbf1d89a9e9c8a66", "signature": "03394bf8deb8781b490ae9266a843fbdf00647947d79e25fcbf1d89a9e9c8a66", "affectsGlobalScope": false}, "../node_modules/@types/node/net.d.ts": {"version": "358398fe4034395d85c87c319cca7a04001434b13dc68d067481ecb374385bfc", "signature": "358398fe4034395d85c87c319cca7a04001434b13dc68d067481ecb374385bfc", "affectsGlobalScope": false}, "../node_modules/@types/node/os.d.ts": {"version": "d9bc6f1917c24d862a68d2633e4a32fd586bfe3e412e5d11fd07d8266b94ced5", "signature": "d9bc6f1917c24d862a68d2633e4a32fd586bfe3e412e5d11fd07d8266b94ced5", "affectsGlobalScope": false}, "../node_modules/@types/node/path.d.ts": {"version": "5fb30076f0e0e5744db8993648bfb67aadd895f439edad5cce039127a87a8a36", "signature": "5fb30076f0e0e5744db8993648bfb67aadd895f439edad5cce039127a87a8a36", "affectsGlobalScope": false}, "../node_modules/@types/node/perf_hooks.d.ts": {"version": "695b442c09d1dd2285df7043c490153dbba928bb16a27421de5f5de5b05989ee", "signature": "695b442c09d1dd2285df7043c490153dbba928bb16a27421de5f5de5b05989ee", "affectsGlobalScope": false}, "../node_modules/@types/node/process.d.ts": {"version": "0e0d58f5e90c0a270dac052b9c5ad8ccdfc8271118c2105b361063218d528d6e", "signature": "0e0d58f5e90c0a270dac052b9c5ad8ccdfc8271118c2105b361063218d528d6e", "affectsGlobalScope": true}, "../node_modules/@types/node/punycode.d.ts": {"version": "4a9008d79750801375605e6cfefa4e04643f20f2aaa58404c6aae1c894e9b049", "signature": "4a9008d79750801375605e6cfefa4e04643f20f2aaa58404c6aae1c894e9b049", "affectsGlobalScope": false}, "../node_modules/@types/node/querystring.d.ts": {"version": "758948c06a0d02623c7d4ed357ffa79bdc170de6e004046678774a1bfa9a29bb", "signature": "758948c06a0d02623c7d4ed357ffa79bdc170de6e004046678774a1bfa9a29bb", "affectsGlobalScope": false}, "../node_modules/@types/node/readline.d.ts": {"version": "2ca26a43dec700c4b0bdc04b123094f4becffda70e3960f3e10b025f7a15ba8f", "signature": "2ca26a43dec700c4b0bdc04b123094f4becffda70e3960f3e10b025f7a15ba8f", "affectsGlobalScope": false}, "../node_modules/@types/node/repl.d.ts": {"version": "30b9c2c0949e27506c7e751bd51749ca5ecb0d0a3ea854064039ffaa3707fad4", "signature": "30b9c2c0949e27506c7e751bd51749ca5ecb0d0a3ea854064039ffaa3707fad4", "affectsGlobalScope": false}, "../node_modules/@types/node/stream.d.ts": {"version": "27317f38ad1c1aa003bafd4fdf92458693d941280b7927baf75fdc7511f73757", "signature": "27317f38ad1c1aa003bafd4fdf92458693d941280b7927baf75fdc7511f73757", "affectsGlobalScope": false}, "../node_modules/@types/node/string_decoder.d.ts": {"version": "7e62aac2cc9c0710d772047ad89e8d7117f52592c791eb995ce1f865fedab432", "signature": "7e62aac2cc9c0710d772047ad89e8d7117f52592c791eb995ce1f865fedab432", "affectsGlobalScope": false}, "../node_modules/@types/node/timers.d.ts": {"version": "b40652bf8ce4a18133b31349086523b219724dca8df3448c1a0742528e7ad5b9", "signature": "b40652bf8ce4a18133b31349086523b219724dca8df3448c1a0742528e7ad5b9", "affectsGlobalScope": false}, "../node_modules/@types/node/tls.d.ts": {"version": "40fa0ba2b8093ec2ec228f2a4a2128c58c91fd2698cae0dd19b0b570ccfcbba4", "signature": "40fa0ba2b8093ec2ec228f2a4a2128c58c91fd2698cae0dd19b0b570ccfcbba4", "affectsGlobalScope": false}, "../node_modules/@types/node/trace_events.d.ts": {"version": "a77fdb357c78b70142b2fdbbfb72958d69e8f765fd2a3c69946c1018e89d4638", "signature": "a77fdb357c78b70142b2fdbbfb72958d69e8f765fd2a3c69946c1018e89d4638", "affectsGlobalScope": false}, "../node_modules/@types/node/tty.d.ts": {"version": "3c2ac350c3baa61fd2b1925844109e098f4376d0768a4643abc82754fd752748", "signature": "3c2ac350c3baa61fd2b1925844109e098f4376d0768a4643abc82754fd752748", "affectsGlobalScope": false}, "../node_modules/@types/node/url.d.ts": {"version": "ae25aec5ed3795a3edfc356a7bc091554917ad0e0009a3cdffd7115ba22bd28d", "signature": "ae25aec5ed3795a3edfc356a7bc091554917ad0e0009a3cdffd7115ba22bd28d", "affectsGlobalScope": false}, "../node_modules/@types/node/util.d.ts": {"version": "0f494abfd66846410cbdf3ee93bdfe5f980e2d03cf2a3f139bb3c91c50a58373", "signature": "0f494abfd66846410cbdf3ee93bdfe5f980e2d03cf2a3f139bb3c91c50a58373", "affectsGlobalScope": false}, "../node_modules/@types/node/v8.d.ts": {"version": "4407bd5f1d6f748590ba125195eb1d7a003c2de2f3b057456d3ac76a742d2561", "signature": "4407bd5f1d6f748590ba125195eb1d7a003c2de2f3b057456d3ac76a742d2561", "affectsGlobalScope": false}, "../node_modules/@types/node/vm.d.ts": {"version": "a0baa0860e17f8ed646315d6330b79c5c192f10e94975ea8c76105d626afb08f", "signature": "a0baa0860e17f8ed646315d6330b79c5c192f10e94975ea8c76105d626afb08f", "affectsGlobalScope": false}, "../node_modules/@types/node/worker_threads.d.ts": {"version": "c0a7074cc53813c32016fd5ea705933ace0aa7679ee687c63c8b099df497d8d0", "signature": "c0a7074cc53813c32016fd5ea705933ace0aa7679ee687c63c8b099df497d8d0", "affectsGlobalScope": false}, "../node_modules/@types/node/zlib.d.ts": {"version": "f9d4531e00e9645678fa8738e8a1eb846bfa4455b4a4605659f231ab4bcbc373", "signature": "f9d4531e00e9645678fa8738e8a1eb846bfa4455b4a4605659f231ab4bcbc373", "affectsGlobalScope": false}, "../node_modules/@types/node/ts3.3/base.d.ts": {"version": "067b1964df87a4fc98ebffbd2bada6d7ed14a5b032f9071ea39478d82e701a99", "signature": "067b1964df87a4fc98ebffbd2bada6d7ed14a5b032f9071ea39478d82e701a99", "affectsGlobalScope": false}, "../node_modules/@types/node/globals.global.d.ts": {"version": "2708349d5a11a5c2e5f3a0765259ebe7ee00cdcc8161cb9990cb4910328442a1", "signature": "2708349d5a11a5c2e5f3a0765259ebe7ee00cdcc8161cb9990cb4910328442a1", "affectsGlobalScope": true}, "../node_modules/@types/node/wasi.d.ts": {"version": "14a6a3cee450438254c004a6b4f1191ec9977186bdeda07764f2a8d90ef71117", "signature": "14a6a3cee450438254c004a6b4f1191ec9977186bdeda07764f2a8d90ef71117", "affectsGlobalScope": false}, "../node_modules/@types/node/ts3.6/base.d.ts": {"version": "1ba358e9eef5efe1b8123b0e96cb3c3c3bc3d5ce32270afbf517b2573ae5b8ce", "signature": "1ba358e9eef5efe1b8123b0e96cb3c3c3bc3d5ce32270afbf517b2573ae5b8ce", "affectsGlobalScope": false}, "../node_modules/@types/node/assert.d.ts": {"version": "235b56ffb69c05129281b81b310521910ecbaeef1b01cdf80ad2cf85b4fdcf0c", "signature": "235b56ffb69c05129281b81b310521910ecbaeef1b01cdf80ad2cf85b4fdcf0c", "affectsGlobalScope": false}, "../node_modules/@types/node/base.d.ts": {"version": "d20072cb51d8baad944bedd935a25c7f10c29744e9a648d2c72c215337356077", "signature": "d20072cb51d8baad944bedd935a25c7f10c29744e9a648d2c72c215337356077", "affectsGlobalScope": false}, "../node_modules/@types/node/index.d.ts": {"version": "b755e9c9cdd3fe80563a564b0b54ec76158a963ce1a4547e9ec4623ecd5b1cb1", "signature": "b755e9c9cdd3fe80563a564b0b54ec76158a963ce1a4547e9ec4623ecd5b1cb1", "affectsGlobalScope": false}, "../../../node_modules/@types/chai/index.d.ts": {"version": "53efac82169bef73ad3641d203d7d7d6c94abaca27d33df11a663be80d735366", "signature": "53efac82169bef73ad3641d203d7d7d6c94abaca27d33df11a663be80d735366", "affectsGlobalScope": true}, "../../../node_modules/@types/minimatch/index.d.ts": {"version": "1d1e6bd176eee5970968423d7e215bfd66828b6db8d54d17afec05a831322633", "signature": "1d1e6bd176eee5970968423d7e215bfd66828b6db8d54d17afec05a831322633", "affectsGlobalScope": false}, "../../../node_modules/@types/glob/index.d.ts": {"version": "393137c76bd922ba70a2f8bf1ade4f59a16171a02fb25918c168d48875b0cfb0", "signature": "393137c76bd922ba70a2f8bf1ade4f59a16171a02fb25918c168d48875b0cfb0", "affectsGlobalScope": false}, "../../../node_modules/@types/json-schema/index.d.ts": {"version": "b2be568d8ce95fcb26eebd04c035d94825655fdf689bf67d799f5ff8cbbb1024", "signature": "b2be568d8ce95fcb26eebd04c035d94825655fdf689bf67d799f5ff8cbbb1024", "affectsGlobalScope": false}, "../../../node_modules/@types/minimist/index.d.ts": {"version": "3602dfff3072caea42f23a9b63fb34a7b0c95a62b93ce2add5fe6b159447845e", "signature": "3602dfff3072caea42f23a9b63fb34a7b0c95a62b93ce2add5fe6b159447845e", "affectsGlobalScope": false}, "../../../node_modules/@types/mocha/index.d.ts": {"version": "c4c03cf65951d980ba618ae9601d10438730803fc9c8a1f7b34af8739981e205", "signature": "c4c03cf65951d980ba618ae9601d10438730803fc9c8a1f7b34af8739981e205", "affectsGlobalScope": true}, "../../../node_modules/@types/normalize-package-data/index.d.ts": {"version": "c9ad058b2cc9ce6dc2ed92960d6d009e8c04bef46d3f5312283debca6869f613", "signature": "c9ad058b2cc9ce6dc2ed92960d6d009e8c04bef46d3f5312283debca6869f613", "affectsGlobalScope": false}, "../../../node_modules/pg-types/index.d.ts": {"version": "f60e3e3060207ac982da13363181fd7ee4beecc19a7c569f0d6bb034331066c2", "signature": "f60e3e3060207ac982da13363181fd7ee4beecc19a7c569f0d6bb034331066c2", "affectsGlobalScope": false}, "../../../node_modules/@types/pg/index.d.ts": {"version": "7508cdeca6eb783aff702fe79f0245704054e9addace5fc07feda8a1aa50b85c", "signature": "7508cdeca6eb783aff702fe79f0245704054e9addace5fc07feda8a1aa50b85c", "affectsGlobalScope": false}, "../../../node_modules/@types/pg-types/index.d.ts": {"version": "060937b9e71c39c855e3572260800c74f248947c5781ddd8db1f3e0a08696c13", "signature": "060937b9e71c39c855e3572260800c74f248947c5781ddd8db1f3e0a08696c13", "affectsGlobalScope": false}}, "options": {"module": 1, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "target": 2, "noImplicitAny": true, "moduleResolution": 2, "sourceMap": true, "outDir": "./", "incremental": true, "baseUrl": "..", "declaration": true, "paths": {"*": ["node_modules/*", "src/types/*"]}, "configFilePath": "../tsconfig.json"}, "referencedMap": {"../../../node_modules/@types/glob/index.d.ts": ["../../../node_modules/@types/minimatch/index.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/index.d.ts"], "../../../node_modules/@types/pg/index.d.ts": ["../../../node_modules/@types/pg/index.d.ts", "../../../node_modules/pg-types/index.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/index.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/tls.d.ts"], "../node_modules/@types/node/base.d.ts": ["../node_modules/@types/node/assert.d.ts", "../node_modules/@types/node/ts3.6/base.d.ts"], "../node_modules/@types/node/child_process.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/cluster.d.ts": ["../node_modules/@types/node/child_process.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/net.d.ts"], "../node_modules/@types/node/crypto.d.ts": ["../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/dgram.d.ts": ["../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/net.d.ts"], "../node_modules/@types/node/domain.d.ts": ["../node_modules/@types/node/events.d.ts"], "../node_modules/@types/node/fs.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/http.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/http2.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/https.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/index.d.ts": ["../node_modules/@types/node/base.d.ts"], "../node_modules/@types/node/inspector.d.ts": ["../node_modules/@types/node/events.d.ts"], "../node_modules/@types/node/net.d.ts": ["../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/perf_hooks.d.ts": ["../node_modules/@types/node/async_hooks.d.ts"], "../node_modules/@types/node/process.d.ts": ["../node_modules/@types/node/tty.d.ts"], "../node_modules/@types/node/readline.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/repl.d.ts": ["../node_modules/@types/node/readline.d.ts", "../node_modules/@types/node/util.d.ts", "../node_modules/@types/node/vm.d.ts"], "../node_modules/@types/node/stream.d.ts": ["../node_modules/@types/node/events.d.ts"], "../node_modules/@types/node/tls.d.ts": ["../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/ts3.3/base.d.ts": ["../node_modules/@types/node/async_hooks.d.ts", "../node_modules/@types/node/buffer.d.ts", "../node_modules/@types/node/child_process.d.ts", "../node_modules/@types/node/cluster.d.ts", "../node_modules/@types/node/console.d.ts", "../node_modules/@types/node/constants.d.ts", "../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/dgram.d.ts", "../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/domain.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/globals.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/http2.d.ts", "../node_modules/@types/node/https.d.ts", "../node_modules/@types/node/inspector.d.ts", "../node_modules/@types/node/module.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/os.d.ts", "../node_modules/@types/node/path.d.ts", "../node_modules/@types/node/perf_hooks.d.ts", "../node_modules/@types/node/process.d.ts", "../node_modules/@types/node/punycode.d.ts", "../node_modules/@types/node/querystring.d.ts", "../node_modules/@types/node/readline.d.ts", "../node_modules/@types/node/repl.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/string_decoder.d.ts", "../node_modules/@types/node/timers.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/trace_events.d.ts", "../node_modules/@types/node/tty.d.ts", "../node_modules/@types/node/url.d.ts", "../node_modules/@types/node/util.d.ts", "../node_modules/@types/node/v8.d.ts", "../node_modules/@types/node/vm.d.ts", "../node_modules/@types/node/worker_threads.d.ts", "../node_modules/@types/node/zlib.d.ts"], "../node_modules/@types/node/ts3.6/base.d.ts": ["../node_modules/@types/node/globals.global.d.ts", "../node_modules/@types/node/ts3.3/base.d.ts", "../node_modules/@types/node/wasi.d.ts"], "../node_modules/@types/node/tty.d.ts": ["../node_modules/@types/node/net.d.ts"], "../node_modules/@types/node/url.d.ts": ["../node_modules/@types/node/querystring.d.ts"], "../node_modules/@types/node/v8.d.ts": ["../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/worker_threads.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/vm.d.ts"], "../node_modules/@types/node/zlib.d.ts": ["../node_modules/@types/node/stream.d.ts"], "../src/b.ts": ["../src/buffer-reader.ts", "../src/buffer-writer.ts", "../src/index.ts"], "../src/inbound-parser.test.ts": ["../node_modules/@types/node/assert.d.ts", "../node_modules/@types/node/stream.d.ts", "../src/index.ts", "../src/messages.ts", "../src/testing/buffer-list.ts", "../src/testing/test-buffers.ts"], "../src/index.ts": ["../src/messages.ts", "../src/parser.ts", "../src/serializer.ts"], "../src/outbound-serializer.test.ts": ["../node_modules/@types/node/assert.d.ts", "../src/serializer.ts", "../src/testing/buffer-list.ts"], "../src/parser.ts": ["../node_modules/@types/node/assert.d.ts", "../node_modules/@types/node/stream.d.ts", "../src/buffer-reader.ts", "../src/messages.ts"], "../src/serializer.ts": ["../src/buffer-writer.ts"], "../src/testing/test-buffers.ts": ["../src/testing/buffer-list.ts"]}, "exportedModulesMap": {"../../../node_modules/@types/glob/index.d.ts": ["../../../node_modules/@types/minimatch/index.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/index.d.ts"], "../../../node_modules/@types/pg/index.d.ts": ["../../../node_modules/@types/pg/index.d.ts", "../../../node_modules/pg-types/index.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/index.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/tls.d.ts"], "../node_modules/@types/node/base.d.ts": ["../node_modules/@types/node/assert.d.ts", "../node_modules/@types/node/ts3.6/base.d.ts"], "../node_modules/@types/node/child_process.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/cluster.d.ts": ["../node_modules/@types/node/child_process.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/net.d.ts"], "../node_modules/@types/node/crypto.d.ts": ["../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/dgram.d.ts": ["../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/net.d.ts"], "../node_modules/@types/node/domain.d.ts": ["../node_modules/@types/node/events.d.ts"], "../node_modules/@types/node/fs.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/http.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/http2.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/https.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/url.d.ts"], "../node_modules/@types/node/index.d.ts": ["../node_modules/@types/node/base.d.ts"], "../node_modules/@types/node/inspector.d.ts": ["../node_modules/@types/node/events.d.ts"], "../node_modules/@types/node/net.d.ts": ["../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/perf_hooks.d.ts": ["../node_modules/@types/node/async_hooks.d.ts"], "../node_modules/@types/node/process.d.ts": ["../node_modules/@types/node/tty.d.ts"], "../node_modules/@types/node/readline.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/repl.d.ts": ["../node_modules/@types/node/readline.d.ts", "../node_modules/@types/node/util.d.ts", "../node_modules/@types/node/vm.d.ts"], "../node_modules/@types/node/stream.d.ts": ["../node_modules/@types/node/events.d.ts"], "../node_modules/@types/node/tls.d.ts": ["../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/ts3.3/base.d.ts": ["../node_modules/@types/node/async_hooks.d.ts", "../node_modules/@types/node/buffer.d.ts", "../node_modules/@types/node/child_process.d.ts", "../node_modules/@types/node/cluster.d.ts", "../node_modules/@types/node/console.d.ts", "../node_modules/@types/node/constants.d.ts", "../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/dgram.d.ts", "../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/domain.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/globals.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/http2.d.ts", "../node_modules/@types/node/https.d.ts", "../node_modules/@types/node/inspector.d.ts", "../node_modules/@types/node/module.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/os.d.ts", "../node_modules/@types/node/path.d.ts", "../node_modules/@types/node/perf_hooks.d.ts", "../node_modules/@types/node/process.d.ts", "../node_modules/@types/node/punycode.d.ts", "../node_modules/@types/node/querystring.d.ts", "../node_modules/@types/node/readline.d.ts", "../node_modules/@types/node/repl.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/string_decoder.d.ts", "../node_modules/@types/node/timers.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/trace_events.d.ts", "../node_modules/@types/node/tty.d.ts", "../node_modules/@types/node/url.d.ts", "../node_modules/@types/node/util.d.ts", "../node_modules/@types/node/v8.d.ts", "../node_modules/@types/node/vm.d.ts", "../node_modules/@types/node/worker_threads.d.ts", "../node_modules/@types/node/zlib.d.ts"], "../node_modules/@types/node/ts3.6/base.d.ts": ["../node_modules/@types/node/globals.global.d.ts", "../node_modules/@types/node/ts3.3/base.d.ts", "../node_modules/@types/node/wasi.d.ts"], "../node_modules/@types/node/tty.d.ts": ["../node_modules/@types/node/net.d.ts"], "../node_modules/@types/node/url.d.ts": ["../node_modules/@types/node/querystring.d.ts"], "../node_modules/@types/node/v8.d.ts": ["../node_modules/@types/node/stream.d.ts"], "../node_modules/@types/node/worker_threads.d.ts": ["../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/vm.d.ts"], "../node_modules/@types/node/zlib.d.ts": ["../node_modules/@types/node/stream.d.ts"], "../src/index.ts": ["../src/messages.ts", "../src/parser.ts", "../src/serializer.ts"], "../src/parser.ts": ["../node_modules/@types/node/stream.d.ts", "../src/messages.ts"], "../src/testing/test-buffers.ts": ["../src/testing/buffer-list.ts"]}, "semanticDiagnosticsPerFile": ["../../../node_modules/@types/chai/index.d.ts", "../../../node_modules/@types/glob/index.d.ts", "../../../node_modules/@types/json-schema/index.d.ts", "../../../node_modules/@types/minimatch/index.d.ts", "../../../node_modules/@types/minimist/index.d.ts", "../../../node_modules/@types/mocha/index.d.ts", "../../../node_modules/@types/normalize-package-data/index.d.ts", "../../../node_modules/@types/pg-types/index.d.ts", "../../../node_modules/@types/pg/index.d.ts", "../../../node_modules/pg-types/index.d.ts", "../../../node_modules/typescript/lib/lib.dom.d.ts", "../../../node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../../node_modules/typescript/lib/lib.es2015.core.d.ts", "../../../node_modules/typescript/lib/lib.es2015.d.ts", "../../../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../../node_modules/typescript/lib/lib.es2016.d.ts", "../../../node_modules/typescript/lib/lib.es2017.d.ts", "../../../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2017.object.d.ts", "../../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../../node_modules/typescript/lib/lib.es2017.string.d.ts", "../../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../../node_modules/typescript/lib/lib.es2018.d.ts", "../../../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../../node_modules/typescript/lib/lib.es5.d.ts", "../../../node_modules/typescript/lib/lib.es6.d.ts", "../../../node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../../node_modules/typescript/lib/lib.scripthost.d.ts", "../../../node_modules/typescript/lib/lib.webworker.importscripts.d.ts", "../node_modules/@types/node/assert.d.ts", "../node_modules/@types/node/async_hooks.d.ts", "../node_modules/@types/node/base.d.ts", "../node_modules/@types/node/buffer.d.ts", "../node_modules/@types/node/child_process.d.ts", "../node_modules/@types/node/cluster.d.ts", "../node_modules/@types/node/console.d.ts", "../node_modules/@types/node/constants.d.ts", "../node_modules/@types/node/crypto.d.ts", "../node_modules/@types/node/dgram.d.ts", "../node_modules/@types/node/dns.d.ts", "../node_modules/@types/node/domain.d.ts", "../node_modules/@types/node/events.d.ts", "../node_modules/@types/node/fs.d.ts", "../node_modules/@types/node/globals.d.ts", "../node_modules/@types/node/globals.global.d.ts", "../node_modules/@types/node/http.d.ts", "../node_modules/@types/node/http2.d.ts", "../node_modules/@types/node/https.d.ts", "../node_modules/@types/node/index.d.ts", "../node_modules/@types/node/inspector.d.ts", "../node_modules/@types/node/module.d.ts", "../node_modules/@types/node/net.d.ts", "../node_modules/@types/node/os.d.ts", "../node_modules/@types/node/path.d.ts", "../node_modules/@types/node/perf_hooks.d.ts", "../node_modules/@types/node/process.d.ts", "../node_modules/@types/node/punycode.d.ts", "../node_modules/@types/node/querystring.d.ts", "../node_modules/@types/node/readline.d.ts", "../node_modules/@types/node/repl.d.ts", "../node_modules/@types/node/stream.d.ts", "../node_modules/@types/node/string_decoder.d.ts", "../node_modules/@types/node/timers.d.ts", "../node_modules/@types/node/tls.d.ts", "../node_modules/@types/node/trace_events.d.ts", "../node_modules/@types/node/ts3.3/base.d.ts", "../node_modules/@types/node/ts3.6/base.d.ts", "../node_modules/@types/node/tty.d.ts", "../node_modules/@types/node/url.d.ts", "../node_modules/@types/node/util.d.ts", "../node_modules/@types/node/v8.d.ts", "../node_modules/@types/node/vm.d.ts", "../node_modules/@types/node/wasi.d.ts", "../node_modules/@types/node/worker_threads.d.ts", "../node_modules/@types/node/zlib.d.ts", "../src/b.ts", "../src/buffer-reader.ts", "../src/buffer-writer.ts", "../src/inbound-parser.test.ts", "../src/index.ts", "../src/messages.ts", "../src/outbound-serializer.test.ts", "../src/parser.ts", "../src/serializer.ts", "../src/testing/buffer-list.ts", "../src/testing/test-buffers.ts", "../src/types/chunky.d.ts"]}, "version": "4.0.3"}