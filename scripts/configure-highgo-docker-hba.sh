#!/bin/bash

# 配置瀚高数据库 Docker 容器的 pg_hba.conf 文件
# 使用方法: ./configure-highgo-docker-hba.sh

CONTAINER_NAME="highgodb-dev"

echo "🔍 配置瀚高数据库 Docker 容器的 pg_hba.conf..."

# 检查容器是否运行
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo "❌ 容器 $CONTAINER_NAME 未运行"
    exit 1
fi

echo "✅ 容器 $CONTAINER_NAME 正在运行"

# 查找 pg_hba.conf 文件位置
echo "🔍 查找 pg_hba.conf 文件位置..."
HBA_PATH=$(docker exec $CONTAINER_NAME find / -name "pg_hba.conf" 2>/dev/null | head -1)

if [ -z "$HBA_PATH" ]; then
    echo "❌ 未找到 pg_hba.conf 文件"
    exit 1
fi

echo "📁 找到 pg_hba.conf 文件: $HBA_PATH"

# 备份原始文件
echo "💾 备份原始 pg_hba.conf 文件..."
docker exec $CONTAINER_NAME cp $HBA_PATH ${HBA_PATH}.backup.$(date +%Y%m%d_%H%M%S)

# 显示当前配置
echo "📋 当前 pg_hba.conf 配置:"
docker exec $CONTAINER_NAME cat $HBA_PATH

echo ""
echo "🔧 添加新的认证规则..."

# 检查是否已存在 highdb_user 的配置
if docker exec $CONTAINER_NAME grep -q "highdb_user" $HBA_PATH; then
    echo "⚠️  highdb_user 的配置已存在，跳过添加"
else
    # 添加 SM3 用户的认证规则
    docker exec $CONTAINER_NAME bash -c "echo 'host    seatom-dev    highdb_user    0.0.0.0/0    sm3' >> $HBA_PATH"
    echo "✅ 已添加 highdb_user 的 SM3 认证规则"
fi

# 检查是否已存在 seatom_user 的配置（MD5 用户）
if docker exec $CONTAINER_NAME grep -q "seatom_user" $HBA_PATH; then
    echo "⚠️  seatom_user 的配置已存在，跳过添加"
else
    # 添加 MD5 用户的认证规则
    docker exec $CONTAINER_NAME bash -c "echo 'host    all           seatom_user    0.0.0.0/0    md5' >> $HBA_PATH"
    echo "✅ 已添加 seatom_user 的 MD5 认证规则"
fi

echo ""
echo "📋 更新后的 pg_hba.conf 配置:"
docker exec $CONTAINER_NAME cat $HBA_PATH

echo ""
echo "🔄 重新加载 PostgreSQL 配置..."

# 尝试重新加载配置
if docker exec $CONTAINER_NAME psql -U postgres -c "SELECT pg_reload_conf();" 2>/dev/null; then
    echo "✅ 配置重新加载成功"
else
    echo "⚠️  尝试使用其他用户重新加载配置..."
    # 尝试其他可能的超级用户
    for user in highgo sysdba postgres; do
        if docker exec $CONTAINER_NAME psql -U $user -c "SELECT pg_reload_conf();" 2>/dev/null; then
            echo "✅ 使用用户 $user 重新加载配置成功"
            break
        fi
    done
fi

echo ""
echo "🎉 pg_hba.conf 配置完成！"
echo ""
echo "📝 配置摘要:"
echo "   - 容器名称: $CONTAINER_NAME"
echo "   - 配置文件: $HBA_PATH"
echo "   - 备份文件: ${HBA_PATH}.backup.$(date +%Y%m%d_%H%M%S)"
echo "   - 新增规则:"
echo "     * host    seatom-dev    highdb_user    0.0.0.0/0    sm3"
echo "     * host    all           seatom_user    0.0.0.0/0    md5"
echo ""
echo "🔧 下一步操作:"
echo "   1. 创建用户: 运行 create-sm3-user-for-seatom-dev.sql"
echo "   2. 测试连接: 运行 test-sm3-user-connection.js"
