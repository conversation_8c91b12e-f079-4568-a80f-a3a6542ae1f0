-- 在瀚高数据库中创建使用 MD5 加密的用户
-- 使用方法: psql -h host -p port -U admin_user -d database -f create-md5-user.sql

-- 1. 检查当前密码加密设置
\echo '当前密码加密方式:'
SHOW password_encryption;

-- 2. 临时设置为 md5 加密
\echo '设置密码加密方式为 md5...'
SET password_encryption TO 'md5';

-- 3. 创建新用户
\echo '创建用户 seatom_user...'
CREATE USER seatom_user PASSWORD 'SeatOM@2025!';

-- 4. 授予基本权限
\echo '授予用户权限...'
GRANT CONNECT ON DATABASE pcis TO seatom_user;
GRANT USAGE ON SCHEMA public TO seatom_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO seatom_user;

-- 授予未来表的权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO seatom_user;

-- 5. 验证用户创建
\echo '验证用户创建结果:'
\du seatom_user

-- 6. 检查用户密码加密方式
\echo '检查用户密码加密方式:'
SELECT rolname, rolpassword FROM pg_authid WHERE rolname = 'seatom_user';

\echo '用户创建完成！'
\echo '请记得在 pg_hba.conf 中添加以下配置行:'
\echo 'host    all             seatom_user     0.0.0.0/0               md5'
\echo '然后执行: SELECT pg_reload_conf();'
