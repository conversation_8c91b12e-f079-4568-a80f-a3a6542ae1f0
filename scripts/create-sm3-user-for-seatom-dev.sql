-- 在瀚高数据库中创建使用 SM3 加密的用户，可访问 seatom-dev 数据库下所有用户的表
-- 使用方法: psql -h host -p port -U admin_user -d seatom-dev -f create-sm3-user-for-seatom-dev.sql

-- 1. 检查当前密码加密设置
\echo '当前密码加密方式:'
SHOW password_encryption;

-- 2. 设置为 sm3 加密（如果当前不是 sm3）
\echo '设置密码加密方式为 sm3...'
SET password_encryption TO 'sm3';

-- 3. 创建新用户，使用 SM3 加密
\echo '创建用户 highdb_user（SM3 加密）...'
CREATE USER highdb_user PASSWORD 'HighDB@SM3#2025!';

-- 4. 授予数据库连接权限
\echo '授予数据库连接权限...'
GRANT CONNECT ON DATABASE "seatom-dev" TO highdb_user;

-- 5. 授予所有 schema 的使用权限
\echo '授予 schema 使用权限...'
GRANT USAGE ON SCHEMA public TO highdb_user;
GRANT USAGE ON SCHEMA information_schema TO highdb_user;

-- 如果有其他自定义 schema，也需要授权
-- GRANT USAGE ON SCHEMA custom_schema TO highdb_user;

-- 6. 授予所有表的访问权限
\echo '授予所有表的访问权限...'

-- 授予 public schema 下所有现有表的权限
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO highdb_user;

-- 授予 public schema 下所有现有序列的权限
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO highdb_user;

-- 授予 public schema 下所有现有函数的执行权限
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO highdb_user;

-- 7. 授予未来创建的对象的权限
\echo '授予未来对象的权限...'

-- 为未来在 public schema 中创建的表授权
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO highdb_user;

-- 为未来在 public schema 中创建的序列授权
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO highdb_user;

-- 为未来在 public schema 中创建的函数授权
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT EXECUTE ON FUNCTIONS TO highdb_user;

-- 8. 如果需要访问其他用户创建的表，需要额外的权限设置
\echo '设置访问其他用户表的权限...'

-- 查询当前数据库中所有的用户和角色
\echo '当前数据库中的用户和角色:'
SELECT rolname FROM pg_roles WHERE rolcanlogin = true;

-- 为其他用户创建的对象授权（需要根据实际情况调整）
-- 这里假设有其他用户如 user1, user2 等
-- ALTER DEFAULT PRIVILEGES FOR ROLE user1 IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO highdb_user;
-- ALTER DEFAULT PRIVILEGES FOR ROLE user2 IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO highdb_user;

-- 9. 授予创建临时表的权限
\echo '授予创建临时表权限...'
GRANT TEMP ON DATABASE "seatom-dev" TO highdb_user;

-- 10. 验证用户创建和权限设置
\echo '验证用户创建结果:'
\du highdb_user

-- 11. 检查用户密码加密方式
\echo '检查用户密码加密方式:'
SELECT rolname, 
       CASE 
         WHEN rolpassword LIKE 'sm3%' THEN 'SM3'
         WHEN rolpassword LIKE 'md5%' THEN 'MD5'
         WHEN rolpassword LIKE 'SCRAM-SHA-256%' THEN 'SCRAM-SHA-256'
         ELSE 'Unknown'
       END as encryption_method
FROM pg_authid 
WHERE rolname = 'highdb_user';

-- 12. 显示用户权限摘要
\echo '用户权限摘要:'
SELECT 
    grantee,
    table_schema,
    privilege_type
FROM information_schema.role_table_grants 
WHERE grantee = 'highdb_user'
ORDER BY table_schema, privilege_type;

\echo ''
\echo '✅ 用户 highdb_user 创建完成！'
\echo ''
\echo '📋 用户信息:'
\echo '   用户名: highdb_user'
\echo '   密码: HighDB@SM3#2025!'
\echo '   加密方式: SM3'
\echo '   数据库: seatom-dev'
\echo ''
\echo '🔐 权限摘要:'
\echo '   ✓ 连接 seatom-dev 数据库'
\echo '   ✓ 使用 public schema'
\echo '   ✓ 对所有现有表: SELECT, INSERT, UPDATE, DELETE'
\echo '   ✓ 对所有现有序列: USAGE, SELECT'
\echo '   ✓ 对所有现有函数: EXECUTE'
\echo '   ✓ 对未来创建的对象: 相同权限'
\echo '   ✓ 创建临时表'
\echo ''
\echo '⚠️  下一步操作:'
\echo '   1. 确保 pg_hba.conf 中配置了 SM3 认证:'
\echo '      host    all    highdb_user    0.0.0.0/0    sm3'
\echo '   2. 重新加载配置: SELECT pg_reload_conf();'
\echo '   3. 测试连接'
