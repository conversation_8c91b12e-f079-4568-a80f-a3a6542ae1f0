const { Pool, Client } = require('highgodb')

// 现场环境连接参数 - 请替换为实际参数
const config = {
  host: '*************', // 替换为现场IP
  port: 5866,
  user: 'highgo', // 或 highdb_user
  password: 'your_password',
  database: 'seatom_dev',
  // 增加超时配置
  connectionTimeoutMillis: 30000, // 30秒连接超时
  idleTimeoutMillis: 30000,
  query_timeout: 30000,
  // 启用详细日志
  ssl: false
}

console.log('🔍 开始调试瀚高数据库连接...')
console.log('📋 连接参数:', {
  host: config.host,
  port: config.port,
  user: config.user,
  database: config.database,
  connectionTimeoutMillis: config.connectionTimeoutMillis
})

// 测试1: 使用 Client 直连
async function testClientConnection() {
  console.log('\n🔗 测试1: Client 直连方式')
  
  const client = new Client(config)
  
  // 监听连接事件
  client.on('connect', () => {
    console.log('✅ Client 连接成功')
  })
  
  client.on('error', (err) => {
    console.log('❌ Client 连接错误:', err.message)
    console.log('   错误代码:', err.code)
    console.log('   错误详情:', err.detail || '无')
  })
  
  try {
    console.log('⏳ 正在连接...')
    await client.connect()
    
    console.log('🎯 执行测试查询...')
    const result = await client.query('SELECT version(), current_database(), current_user')
    
    console.log('✅ 查询成功:', result.rows[0])
    
    await client.end()
    console.log('🔒 连接已关闭')
    
  } catch (error) {
    console.log('❌ Client 连接失败:')
    console.log('   错误信息:', error.message)
    console.log('   错误代码:', error.code)
    console.log('   错误栈:', error.stack)
  }
}

// 测试2: 使用连接池
async function testPoolConnection() {
  console.log('\n🏊 测试2: 连接池方式')
  
  const pool = new Pool(config)
  
  // 监听连接池事件
  pool.on('connect', () => {
    console.log('✅ 连接池建立新连接')
  })
  
  pool.on('error', (err) => {
    console.log('❌ 连接池错误:', err.message)
    console.log('   错误代码:', err.code)
  })
  
  try {
    console.log('⏳ 正在通过连接池连接...')
    const client = await pool.connect()
    
    console.log('🎯 执行测试查询...')
    const result = await client.query('SELECT version(), current_database(), current_user')
    
    console.log('✅ 查询成功:', result.rows[0])
    
    client.release() // 释放连接回池
    await pool.end()
    console.log('🔒 连接池已关闭')
    
  } catch (error) {
    console.log('❌ 连接池连接失败:')
    console.log('   错误信息:', error.message)
    console.log('   错误代码:', error.code)
    console.log('   错误栈:', error.stack)
    
    try {
      await pool.end()
    } catch (e) {
      console.log('⚠️  关闭连接池时出错:', e.message)
    }
  }
}

// 测试3: 网络连通性检查
function testNetworkConnectivity() {
  console.log('\n🌐 测试3: 网络连通性检查')
  
  const net = require('net')
  const socket = new net.Socket()
  
  const timeout = setTimeout(() => {
    socket.destroy()
    console.log('❌ 网络连接超时 - 无法连接到服务器')
    console.log('💡 建议检查:')
    console.log('   1. 服务器IP地址是否正确')
    console.log('   2. 端口5866是否开放')
    console.log('   3. 防火墙设置')
    console.log('   4. 网络路由')
  }, 10000) // 10秒超时
  
  socket.on('connect', () => {
    clearTimeout(timeout)
    console.log('✅ 网络连接成功 - 可以访问服务器端口')
    socket.destroy()
  })
  
  socket.on('error', (err) => {
    clearTimeout(timeout)
    console.log('❌ 网络连接失败:', err.message)
    console.log('💡 常见原因:')
    console.log('   - ECONNREFUSED: 端口未开放或服务未启动')
    console.log('   - ETIMEDOUT: 网络超时或防火墙阻止')
    console.log('   - EHOSTUNREACH: 主机不可达')
  })
  
  console.log(`⏳ 正在测试网络连接 ${config.host}:${config.port}...`)
  socket.connect(config.port, config.host)
}

// 执行所有测试
async function runAllTests() {
  try {
    // 先测试网络连通性
    testNetworkConnectivity()
    
    // 等待网络测试完成
    await new Promise(resolve => setTimeout(resolve, 12000))
    
    // 然后测试数据库连接
    await testClientConnection()
    await testPoolConnection()
    
  } catch (error) {
    console.log('🚨 测试过程中出现未捕获错误:', error)
  }
}

// 启动测试
runAllTests()
