const { Pool, Client } = require('highgodb')
////使用连接池的方式
//const pool = new Pool({
//    user: 'sysdba',
//    host: '***************',
//    database: 'highgo',
//    password: 'Qwer@1234',
//    password: 'Qwer@1234',
//    port: 5866,
//})
//pool.query('select * from  nodejs_test;', (err, res) => {
//    console.log(err, res)
//    pool.end()
//})

//使用 client 方式
const client = new Client({
    host: 'local',
    user: "sysdba",
    password: "Qwer@1234",
    database: "highgo",
    port: 5866,
})
client.connect()
client.query('select * from  nodejs_test;', (err, res) => {
    console.log(err, res)
    client.end()
})

console.log('Press any key to exit');

process.stdin.setRawMode(true);
process.stdin.resume();
process.stdin.on('data', process.exit.bind(process, 0));