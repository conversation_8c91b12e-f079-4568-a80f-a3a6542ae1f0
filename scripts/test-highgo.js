const { Pool, Client } = require('highgodb')

console.log('🔍 测试瀚高数据库连接 (使用官方 highgodb 包)...')

// 使用连接池的方式 (MD5 用户)
const pool = new Pool({
    user: 'highgo',
    host: 'localhost',
    database: 'seatom_dev',
    password: 'Highgo@123',
    port: 5866,
})

console.log('📋 连接参数 (Pool):')
console.log('   主机: localhost')
console.log('   端口: 5866')
console.log('   用户: highgo (MD5)')
console.log('   数据库: seatom_dev')

pool.query('SELECT version(), current_user, current_database();', (err, res) => {
    if (err) {
        console.log('❌ Pool 连接失败:', err.message)
    } else {
        console.log('✅ Pool 连接成功!')
        console.log('📊 查询结果:', res.rows[0])
    }
    pool.end()

    // 测试 Client 方式
    testClient()
})

// 使用 client 方式 (SM3 用户)
function testClient() {
    console.log('\n🔄 测试 Client 方式连接 (SM3 用户)...')

    const client = new Client({
        host: 'localhost',
        user: "highdb_user",
        password: "Wufei123!",
        database: "seatom_dev",
        port: 5866,
    })

    console.log('📋 连接参数 (Client):')
    console.log('   主机: localhost')
    console.log('   端口: 5866')
    console.log('   用户: highdb_user (SM3)')
    console.log('   数据库: seatom_dev')

    client.connect()
    client.query('SELECT version(), current_user, current_database();', (err, res) => {
        if (err) {
            console.log('❌ Client 连接失败:', err.message)
        } else {
            console.log('✅ Client 连接成功!')
            console.log('📊 查询结果:', res.rows[0])
        }
        client.end()
    })
}

console.log('Press any key to exit');

process.stdin.setRawMode(true);
process.stdin.resume();
process.stdin.on('data', process.exit.bind(process, 0));