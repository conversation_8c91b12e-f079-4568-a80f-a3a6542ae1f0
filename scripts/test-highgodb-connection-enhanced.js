#!/usr/bin/env node

/**
 * 瀚高数据库连接测试脚本 - 增强版
 * 专门针对瀚高数据库的兼容性问题进行优化
 * 使用方法: node test-highgodb-connection-enhanced.js <host> <port> <username> <password> <database>
 * 示例: node test-highgodb-connection-enhanced.js ************* 5866 highgo mypassword testdb
 */

const net = require('net')
const { Pool, Client } = require('pg')

// 获取命令行参数
const args = process.argv.slice(2)
if (args.length < 5) {
  console.log('使用方法: node test-highgodb-connection-enhanced.js <host> <port> <username> <password> <database>')
  console.log('示例: node test-highgodb-connection-enhanced.js ************* 5866 highgo mypassword testdb')
  process.exit(1)
}

const [host, port, username, password, database] = args

console.log('🔍 开始测试瀚高数据库连接 (增强版)...')
console.log('📋 连接参数:')
console.log(`   主机: ${host}`)
console.log(`   端口: ${port}`)
console.log(`   用户: ${username}`)
console.log(`   数据库: ${database}`)
console.log('=' * 50)

// 瀚高数据库兼容性配置
const highgoConfig = {
  host,
  port: parseInt(port),
  user: username,
  password,
  database,
  // 关键的兼容性设置
  ssl: false,
  connectionTimeoutMillis: 30000,
  idleTimeoutMillis: 30000,
  max: 1, // 连接池大小设为1
  // 禁用可能导致问题的特性
  application_name: 'highgodb-test',
  statement_timeout: 0,
  query_timeout: 30000,
  // 尝试使用更低版本的协议
  options: '--client_encoding=UTF8'
}

// 测试1: TCP连接测试
async function testTcpConnection() {
  return new Promise((resolve) => {
    console.log('🔌 测试1: TCP端口连通性...')
    const startTime = Date.now()
    const socket = new net.Socket()
    
    const timeout = setTimeout(() => {
      socket.destroy()
      const duration = Date.now() - startTime
      console.log(`❌ TCP连接超时 (${duration}ms)`)
      resolve(false)
    }, 10000)

    socket.connect(parseInt(port), host, () => {
      clearTimeout(timeout)
      socket.destroy()
      const duration = Date.now() - startTime
      console.log(`✅ TCP连接成功 (${duration}ms)`)
      resolve(true)
    })

    socket.on('error', (err) => {
      clearTimeout(timeout)
      const duration = Date.now() - startTime
      console.log(`❌ TCP连接失败: ${err.message} (${duration}ms)`)
      console.log(`   错误代码: ${err.code}`)
      resolve(false)
    })
  })
}

// 测试2: 使用原生 Client 连接
async function testClientConnection() {
  return new Promise((resolve) => {
    console.log('🐘 测试2: PostgreSQL Client 连接...')
    const startTime = Date.now()
    
    const client = new Client(highgoConfig)

    const timeout = setTimeout(() => {
      client.end()
      const duration = Date.now() - startTime
      console.log(`❌ Client连接超时 (${duration}ms)`)
      resolve(false)
    }, 35000)

    client.connect((err) => {
      clearTimeout(timeout)
      const duration = Date.now() - startTime
      
      if (err) {
        console.log(`❌ Client连接失败: ${err.message} (${duration}ms)`)
        console.log(`   错误代码: ${err.code}`)
        
        // 详细的错误分析
        if (err.message.includes('authentication')) {
          console.log('   💡 认证相关错误，可能的原因:')
          console.log('      - 用户名或密码错误')
          console.log('      - 瀚高数据库使用了特殊的认证方法')
          console.log('      - pg_hba.conf 配置问题')
        } else if (err.message.includes('Unknown authentication')) {
          console.log('   💡 未知认证类型错误，建议:')
          console.log('      - 检查瀚高数据库版本兼容性')
          console.log('      - 尝试使用瀚高专用的驱动程序')
          console.log('      - 检查数据库的认证配置')
        }
        
        resolve(false)
      } else {
        console.log(`✅ Client连接成功 (${duration}ms)`)
        client.end()
        resolve(true)
      }
    })
  })
}

// 测试3: 使用连接池
async function testPoolConnection() {
  return new Promise((resolve) => {
    console.log('🏊 测试3: PostgreSQL Pool 连接...')
    const startTime = Date.now()
    
    const pool = new Pool(highgoConfig)

    const timeout = setTimeout(() => {
      pool.end()
      const duration = Date.now() - startTime
      console.log(`❌ Pool连接超时 (${duration}ms)`)
      resolve(false)
    }, 35000)

    pool.connect((err, client, release) => {
      clearTimeout(timeout)
      const duration = Date.now() - startTime
      
      if (err) {
        console.log(`❌ Pool连接失败: ${err.message} (${duration}ms)`)
        console.log(`   错误代码: ${err.code}`)
        pool.end()
        resolve(false)
      } else {
        console.log(`✅ Pool连接成功 (${duration}ms)`)
        release()
        pool.end()
        resolve(true)
      }
    })
  })
}

// 测试4: 执行查询
async function testQuery() {
  return new Promise((resolve) => {
    console.log('📊 测试4: 执行数据库查询...')
    const startTime = Date.now()
    
    const client = new Client(highgoConfig)

    client.connect((err) => {
      if (err) {
        const duration = Date.now() - startTime
        console.log(`❌ 连接失败，跳过查询测试 (${duration}ms)`)
        resolve(false)
        return
      }

      // 使用更简单的查询，避免可能的兼容性问题
      client.query('SELECT 1 as test_value', (err, result) => {
        const duration = Date.now() - startTime
        
        if (err) {
          console.log(`❌ 简单查询失败: ${err.message} (${duration}ms)`)
          client.end()
          
          // 尝试更基础的查询
          console.log('🔄 尝试更基础的查询...')
          testBasicQuery().then(resolve)
        } else {
          console.log(`✅ 简单查询成功 (${duration}ms)`)
          console.log(`   结果: ${result.rows[0].test_value}`)
          
          // 尝试获取数据库信息
          client.query('SELECT version()', (err2, result2) => {
            client.end()
            if (err2) {
              console.log(`⚠️  版本查询失败: ${err2.message}`)
            } else {
              console.log(`📋 数据库版本: ${result2.rows[0].version}`)
            }
            resolve(true)
          })
        }
      })
    })
  })
}

// 测试5: 基础查询
async function testBasicQuery() {
  return new Promise((resolve) => {
    console.log('🔧 测试5: 基础查询测试...')
    const startTime = Date.now()
    
    const client = new Client(highgoConfig)

    client.connect((err) => {
      if (err) {
        const duration = Date.now() - startTime
        console.log(`❌ 基础查询连接失败 (${duration}ms)`)
        resolve(false)
        return
      }

      // 最基础的查询
      client.query('SELECT current_user', (err, result) => {
        const duration = Date.now() - startTime
        client.end()
        
        if (err) {
          console.log(`❌ 基础查询失败: ${err.message} (${duration}ms)`)
          resolve(false)
        } else {
          console.log(`✅ 基础查询成功 (${duration}ms)`)
          console.log(`   当前用户: ${result.rows[0].current_user}`)
          resolve(true)
        }
      })
    })
  })
}

// 主测试函数
async function runTests() {
  try {
    const tcpResult = await testTcpConnection()
    console.log('')
    
    if (tcpResult) {
      const clientResult = await testClientConnection()
      console.log('')
      
      if (clientResult) {
        const poolResult = await testPoolConnection()
        console.log('')
        
        if (poolResult) {
          await testQuery()
        }
      } else {
        console.log('⚠️  由于Client连接失败，跳过后续测试')
        console.log('💡 建议检查:')
        console.log('   1. 瀚高数据库是否支持标准PostgreSQL协议')
        console.log('   2. 是否需要使用瀚高专用的数据库驱动')
        console.log('   3. 数据库的认证配置是否正确')
      }
    }
    
    console.log('')
    console.log('🏁 测试完成!')
    console.log('=' * 50)
    
  } catch (error) {
    console.error('💥 测试过程中发生错误:', error.message)
    console.error('   堆栈信息:', error.stack)
    process.exit(1)
  }
}

// 运行测试
runTests()
