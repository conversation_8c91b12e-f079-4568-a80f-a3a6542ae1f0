#!/usr/bin/env node

/**
 * 瀚高数据库连接测试脚本
 * 使用方法: node test-highgodb-connection.js <host> <port> <username> <password> <database>
 * 示例: node test-highgodb-connection.js ************* 5866 highgo mypassword testdb
 */

const net = require('net')
const pg = require('pg')

// 获取命令行参数
const args = process.argv.slice(2)
if (args.length < 5) {
  console.log('使用方法: node test-highgodb-connection.js <host> <port> <username> <password> <database>')
  console.log('示例: node test-highgodb-connection.js ************* 5866 highgo mypassword testdb')
  process.exit(1)
}

const [host, port, username, password, database] = args

console.log('🔍 开始测试瀚高数据库连接...')
console.log('📋 连接参数:')
console.log(`   主机: ${host}`)
console.log(`   端口: ${port}`)
console.log(`   用户: ${username}`)
console.log(`   数据库: ${database}`)
console.log('=' * 50)

// 测试1: TCP连接测试
async function testTcpConnection() {
  return new Promise((resolve) => {
    console.log('🔌 测试1: TCP端口连通性...')
    const startTime = Date.now()
    const socket = new net.Socket()
    
    const timeout = setTimeout(() => {
      socket.destroy()
      const duration = Date.now() - startTime
      console.log(`❌ TCP连接超时 (${duration}ms)`)
      resolve(false)
    }, 10000)

    socket.connect(parseInt(port), host, () => {
      clearTimeout(timeout)
      socket.destroy()
      const duration = Date.now() - startTime
      console.log(`✅ TCP连接成功 (${duration}ms)`)
      resolve(true)
    })

    socket.on('error', (err) => {
      clearTimeout(timeout)
      const duration = Date.now() - startTime
      console.log(`❌ TCP连接失败: ${err.message} (${duration}ms)`)
      console.log(`   错误代码: ${err.code}`)
      resolve(false)
    })
  })
}

// 测试2: PostgreSQL协议连接测试
async function testPgConnection() {
  return new Promise((resolve) => {
    console.log('🐘 测试2: PostgreSQL协议连接...')
    const startTime = Date.now()

    const client = new pg.Client({
      host,
      port: parseInt(port),
      user: username,
      password,
      database,
      connectionTimeoutMillis: 15000,
      // 添加瀚高数据库兼容性配置
      ssl: false,
      // 尝试使用更兼容的配置
      application_name: 'highgodb-test',
      // 禁用一些可能导致兼容性问题的特性
      statement_timeout: 0,
      query_timeout: 0
    })

    const timeout = setTimeout(() => {
      client.end()
      const duration = Date.now() - startTime
      console.log(`❌ PostgreSQL连接超时 (${duration}ms)`)
      resolve(false)
    }, 20000)

    client.connect((err) => {
      clearTimeout(timeout)
      const duration = Date.now() - startTime
      
      if (err) {
        console.log(`❌ PostgreSQL连接失败: ${err.message} (${duration}ms)`)
        console.log(`   错误代码: ${err.code}`)
        
        // 提供具体的错误分析
        if (err.code === 'ECONNREFUSED') {
          console.log('   💡 建议: 检查数据库服务是否启动')
        } else if (err.code === 'ETIMEDOUT') {
          console.log('   💡 建议: 检查网络连接和防火墙设置')
        } else if (err.message.includes('authentication')) {
          console.log('   💡 建议: 检查用户名和密码')
        } else if (err.message.includes('database') && err.message.includes('does not exist')) {
          console.log('   💡 建议: 检查数据库名称')
        }
        
        resolve(false)
      } else {
        console.log(`✅ PostgreSQL连接成功 (${duration}ms)`)
        client.end()
        resolve(true)
      }
    })
  })
}

// 测试3: 简单查询测试
async function testSimpleQuery() {
  return new Promise((resolve) => {
    console.log('📊 测试3: 执行简单查询...')
    const startTime = Date.now()

    const client = new pg.Client({
      host,
      port: parseInt(port),
      user: username,
      password,
      database,
      connectionTimeoutMillis: 15000,
      // 添加瀚高数据库兼容性配置
      ssl: false,
      application_name: 'highgodb-test',
      statement_timeout: 0,
      query_timeout: 0
    })

    client.connect((err) => {
      if (err) {
        const duration = Date.now() - startTime
        console.log(`❌ 连接失败，跳过查询测试 (${duration}ms)`)
        resolve(false)
        return
      }

      client.query('SELECT version() as version, current_database() as database, current_user as user', (err, result) => {
        const duration = Date.now() - startTime
        client.end()
        
        if (err) {
          console.log(`❌ 查询失败: ${err.message} (${duration}ms)`)
          resolve(false)
        } else {
          console.log(`✅ 查询成功 (${duration}ms)`)
          console.log('📋 数据库信息:')
          console.log(`   版本: ${result.rows[0].version}`)
          console.log(`   数据库: ${result.rows[0].database}`)
          console.log(`   用户: ${result.rows[0].user}`)
          resolve(true)
        }
      })
    })
  })
}

// 主测试函数
async function runTests() {
  try {
    const tcpResult = await testTcpConnection()
    console.log('')
    
    if (tcpResult) {
      const pgResult = await testPgConnection()
      console.log('')
      
      if (pgResult) {
        await testSimpleQuery()
      }
    }
    
    console.log('')
    console.log('🏁 测试完成!')
    console.log('=' * 50)
    
  } catch (error) {
    console.error('💥 测试过程中发生错误:', error.message)
    process.exit(1)
  }
}

// 运行测试
runTests()
