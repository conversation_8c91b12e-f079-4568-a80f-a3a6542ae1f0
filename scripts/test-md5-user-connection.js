/**
 * 测试使用 MD5 加密用户连接瀚高数据库
 * 使用方法: node test-md5-user-connection.js
 */

const pg = require('pg')

const host = 'localhost'
const port = 5866
const username = 'highgo'
const password = 'Highgo@123'
const database = 'seatom_dev'

console.log('🔍 测试 MD5 用户连接瀚高数据库...')
console.log('📋 连接参数:')
console.log(`   主机: ${host}`)
console.log(`   端口: ${port}`)
console.log(`   用户: ${username}`)
console.log(`   数据库: ${database}`)
console.log(`   加密方式: MD5`)
console.log('=' * 60)

// MD5 用户连接配置
const md5Config = {
  host,
  port: parseInt(port),
  user: username,
  password,
  database,
  ssl: false,
  connectionTimeoutMillis: 15000,
  idleTimeoutMillis: 30000,
  application_name: 'seatom-md5-test'
}

async function testMD5Connection() {
  console.log('🔐 测试 MD5 用户连接...')
  const startTime = Date.now()
  
  try {
    const client = new pg.Client(md5Config)
    
    await client.connect()
    console.log(`✅ MD5 用户连接成功! (${Date.now() - startTime}ms)`)
    
    // 测试简单查询
    const result = await client.query('SELECT version(), current_user, current_database()')
    console.log('📊 查询结果:')
    console.log(`   版本: ${result.rows[0].version.substring(0, 50)}...`)
    console.log(`   当前用户: ${result.rows[0].current_user}`)
    console.log(`   当前数据库: ${result.rows[0].current_database}`)
    
    // 测试权限
    try {
      const tables = await client.query(`
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        LIMIT 5
      `)
      console.log(`📋 可访问的表数量: ${tables.rows.length}`)
      if (tables.rows.length > 0) {
        console.log('   示例表:')
        tables.rows.forEach(row => {
          console.log(`     - ${row.schemaname}.${row.tablename}`)
        })
      }
    } catch (permError) {
      console.log('⚠️  权限测试失败:', permError.message)
    }
    
    await client.end()
    return true
    
  } catch (error) {
    const duration = Date.now() - startTime
    console.log(`❌ MD5 用户连接失败 (${duration}ms)`)
    console.log('错误详情:', error.message)
    return false
  }
}

async function main() {
  const success = await testMD5Connection()
  
  if (success) {
    console.log('\n🎉 MD5 用户连接测试成功!')
    console.log('现在可以在你的应用中使用这个用户连接瀚高数据库了。')
  } else {
    console.log('\n❌ MD5 用户连接测试失败!')
    console.log('请检查用户创建和 pg_hba.conf 配置。')
    process.exit(1)
  }
}

main().catch(console.error)
