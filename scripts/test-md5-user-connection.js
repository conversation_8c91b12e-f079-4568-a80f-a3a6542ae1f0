/**
 * 测试使用 MD5 加密用户连接瀚高数据库
 * 使用方法: node test-md5-user-connection.js host port username password database
 */

const pg = require('pg')

// 获取命令行参数
const args = process.argv.slice(2)
if (args.length !== 5) {
  console.log('使用方法: node test-md5-user-connection.js <host> <port> <username> <password> <database>')
  console.log('示例: node test-md5-user-connection.js ************ 5866 seatom_user "SeatOM@2025!" pcis')
  process.exit(1)
}

const [host, port, username, password, database] = args

console.log('🔍 测试 MD5 用户连接瀚高数据库...')
console.log('📋 连接参数:')
console.log(`   主机: ${host}`)
console.log(`   端口: ${port}`)
console.log(`   用户: ${username}`)
console.log(`   数据库: ${database}`)
console.log('=' * 50)

// MD5 用户连接配置
const md5Config = {
  host,
  port: parseInt(port),
  user: username,
  password,
  database,
  // MD5 用户的标准配置
  ssl: false,
  connectionTimeoutMillis: 15000,
  idleTimeoutMillis: 30000,
  application_name: 'seatom-md5-test'
}

async function testMD5Connection() {
  console.log('🔐 测试 MD5 用户连接...')
  const startTime = Date.now()
  
  try {
    const client = new pg.Client(md5Config)
    
    await client.connect()
    console.log(`✅ MD5 用户连接成功! (${Date.now() - startTime}ms)`)
    
    // 测试简单查询
    const result = await client.query('SELECT version(), current_user, current_database()')
    console.log('📊 查询结果:')
    console.log(`   版本: ${result.rows[0].version}`)
    console.log(`   当前用户: ${result.rows[0].current_user}`)
    console.log(`   当前数据库: ${result.rows[0].current_database}`)
    
    // 测试权限
    try {
      const tables = await client.query(`
        SELECT schemaname, tablename 
        FROM pg_tables 
        WHERE schemaname = 'public' 
        LIMIT 5
      `)
      console.log(`📋 可访问的表数量: ${tables.rows.length}`)
      if (tables.rows.length > 0) {
        console.log('   示例表:')
        tables.rows.forEach(row => {
          console.log(`     - ${row.schemaname}.${row.tablename}`)
        })
      }
    } catch (permError) {
      console.log('⚠️  权限测试失败:', permError.message)
    }
    
    await client.end()
    return true
    
  } catch (error) {
    const duration = Date.now() - startTime
    console.log(`❌ MD5 用户连接失败 (${duration}ms)`)
    console.log('错误详情:', error.message)
    
    // 提供具体的错误分析
    if (error.message.includes('authentication')) {
      console.log('💡 认证失败可能的原因:')
      console.log('   1. 用户密码不正确')
      console.log('   2. pg_hba.conf 中未配置 md5 认证')
      console.log('   3. 用户不存在或被禁用')
    } else if (error.message.includes('ECONNREFUSED')) {
      console.log('💡 连接被拒绝，请检查:')
      console.log('   1. 数据库服务是否启动')
      console.log('   2. 主机地址和端口是否正确')
      console.log('   3. 防火墙设置')
    } else if (error.message.includes('timeout')) {
      console.log('💡 连接超时，请检查:')
      console.log('   1. 网络连接')
      console.log('   2. 数据库负载')
      console.log('   3. 防火墙设置')
    }
    
    return false
  }
}

async function main() {
  const success = await testMD5Connection()
  
  if (success) {
    console.log('\n🎉 MD5 用户连接测试成功!')
    console.log('现在可以在你的应用中使用这个用户连接瀚高数据库了。')
  } else {
    console.log('\n❌ MD5 用户连接测试失败!')
    console.log('请检查用户创建和 pg_hba.conf 配置。')
    process.exit(1)
  }
}

main().catch(console.error)
