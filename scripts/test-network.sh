#!/bin/bash

# 瀚高数据库网络连接测试脚本
# 使用方法: ./test-network.sh <host> <port>
# 示例: ./test-network.sh ************* 5866

if [ $# -ne 2 ]; then
    echo "使用方法: $0 <host> <port>"
    echo "示例: $0 ************* 5866"
    exit 1
fi

HOST=$1
PORT=$2

echo "🔍 开始测试网络连接到 $HOST:$PORT"
echo "=================================================="

# 测试1: ping测试
echo "🏓 测试1: Ping连通性..."
if ping -c 3 -W 3 $HOST > /dev/null 2>&1; then
    echo "✅ Ping成功"
else
    echo "❌ Ping失败"
fi
echo ""

# 测试2: 使用bash内置的网络重定向测试端口
echo "🔌 测试2: 端口连通性..."
if timeout 10 bash -c "</dev/tcp/$HOST/$PORT" 2>/dev/null; then
    echo "✅ 端口 $PORT 开放"
else
    echo "❌ 端口 $PORT 不通或超时"
fi
echo ""

# 测试3: 使用telnet（如果可用）
echo "📞 测试3: Telnet测试..."
if command -v telnet >/dev/null 2>&1; then
    echo "正在测试telnet连接..."
    (echo "quit"; sleep 2) | timeout 10 telnet $HOST $PORT 2>&1 | head -5
else
    echo "⚠️  telnet命令不可用"
fi
echo ""

# 测试4: 使用nc（如果可用）
echo "🌐 测试4: Netcat测试..."
if command -v nc >/dev/null 2>&1; then
    if timeout 10 nc -zv $HOST $PORT 2>&1; then
        echo "✅ Netcat连接成功"
    else
        echo "❌ Netcat连接失败"
    fi
else
    echo "⚠️  nc命令不可用"
fi
echo ""

# 测试5: 检查路由
echo "🗺️  测试5: 路由信息..."
if command -v ip >/dev/null 2>&1; then
    echo "路由到 $HOST:"
    ip route get $HOST 2>/dev/null || echo "无法获取路由信息"
else
    echo "⚠️  ip命令不可用"
fi
echo ""

# 测试6: 检查本地网络配置
echo "⚙️  测试6: 本地网络配置..."
echo "本机IP地址:"
if command -v ip >/dev/null 2>&1; then
    ip addr show | grep "inet " | grep -v "127.0.0.1"
elif command -v ifconfig >/dev/null 2>&1; then
    ifconfig | grep "inet " | grep -v "127.0.0.1"
else
    echo "⚠️  无法获取网络配置"
fi
echo ""

echo "🏁 网络测试完成!"
echo "=================================================="
