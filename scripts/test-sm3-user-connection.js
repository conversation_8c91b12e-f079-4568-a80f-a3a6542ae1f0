/**
 * 测试使用 SM3 加密用户连接瀚高数据库 seatom-dev
 * 使用方法: node test-sm3-user-connection.js host port
 */

const pg = require('pg')

// 获取命令行参数
const args = process.argv.slice(2)
if (args.length !== 2) {
  console.log('使用方法: node test-sm3-user-connection.js <host> <port>')
  console.log('示例: node test-sm3-user-connection.js ************ 5866')
  process.exit(1)
}

const [host, port] = args
const username = 'highdb_user'
const password = 'HighDB@SM3#2025!'
const database = 'seatom-dev'

console.log('🔍 测试 SM3 用户连接瀚高数据库...')
console.log('📋 连接参数:')
console.log(`   主机: ${host}`)
console.log(`   端口: ${port}`)
console.log(`   用户: ${username}`)
console.log(`   数据库: ${database}`)
console.log(`   加密方式: SM3`)
console.log('=' * 60)

// SM3 用户连接配置
const sm3Config = {
  host,
  port: parseInt(port),
  user: username,
  password,
  database,
  // SM3 用户的配置
  ssl: false,
  connectionTimeoutMillis: 30000,
  idleTimeoutMillis: 30000,
  application_name: 'seatom-sm3-test'
}

async function testSM3Connection() {
  console.log('🔐 测试 SM3 用户连接...')
  const startTime = Date.now()
  
  try {
    const client = new pg.Client(sm3Config)
    
    await client.connect()
    console.log(`✅ SM3 用户连接成功! (${Date.now() - startTime}ms)`)
    
    // 测试基本信息查询
    const basicInfo = await client.query(`
      SELECT 
        version() as version,
        current_user as current_user,
        current_database() as current_database,
        current_schema() as current_schema
    `)
    
    console.log('📊 数据库信息:')
    console.log(`   版本: ${basicInfo.rows[0].version.substring(0, 50)}...`)
    console.log(`   当前用户: ${basicInfo.rows[0].current_user}`)
    console.log(`   当前数据库: ${basicInfo.rows[0].current_database}`)
    console.log(`   当前模式: ${basicInfo.rows[0].current_schema}`)
    
    // 测试权限 - 查看可访问的表
    try {
      const tables = await client.query(`
        SELECT 
          schemaname, 
          tablename,
          tableowner
        FROM pg_tables 
        WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
        ORDER BY schemaname, tablename
        LIMIT 10
      `)
      
      console.log(`📋 可访问的表 (前10个):`)
      if (tables.rows.length > 0) {
        tables.rows.forEach(row => {
          console.log(`   - ${row.schemaname}.${row.tablename} (owner: ${row.tableowner})`)
        })
      } else {
        console.log('   暂无可访问的用户表')
      }
      
      // 统计总表数
      const tableCount = await client.query(`
        SELECT COUNT(*) as total_tables
        FROM pg_tables 
        WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
      `)
      console.log(`   总计: ${tableCount.rows[0].total_tables} 个用户表`)
      
    } catch (permError) {
      console.log('⚠️  表权限测试失败:', permError.message)
    }
    
    // 测试序列权限
    try {
      const sequences = await client.query(`
        SELECT 
          schemaname, 
          sequencename,
          sequenceowner
        FROM pg_sequences 
        WHERE schemaname NOT IN ('information_schema', 'pg_catalog')
        LIMIT 5
      `)
      
      if (sequences.rows.length > 0) {
        console.log(`🔢 可访问的序列:`)
        sequences.rows.forEach(row => {
          console.log(`   - ${row.schemaname}.${row.sequencename} (owner: ${row.sequenceowner})`)
        })
      }
    } catch (seqError) {
      console.log('⚠️  序列权限测试失败:', seqError.message)
    }
    
    // 测试用户权限详情
    try {
      const privileges = await client.query(`
        SELECT 
          table_schema,
          COUNT(*) as table_count,
          string_agg(DISTINCT privilege_type, ', ') as privileges
        FROM information_schema.role_table_grants 
        WHERE grantee = current_user
          AND table_schema NOT IN ('information_schema', 'pg_catalog')
        GROUP BY table_schema
        ORDER BY table_schema
      `)
      
      if (privileges.rows.length > 0) {
        console.log(`🔑 权限详情:`)
        privileges.rows.forEach(row => {
          console.log(`   - Schema: ${row.table_schema}`)
          console.log(`     表数量: ${row.table_count}`)
          console.log(`     权限: ${row.privileges}`)
        })
      }
    } catch (privError) {
      console.log('⚠️  权限详情查询失败:', privError.message)
    }
    
    // 测试创建临时表权限
    try {
      await client.query('CREATE TEMP TABLE test_temp (id int, name text)')
      await client.query('INSERT INTO test_temp VALUES (1, \'test\')')
      const tempResult = await client.query('SELECT * FROM test_temp')
      console.log(`🗂️  临时表测试: ✅ 成功 (插入 ${tempResult.rows.length} 行)`)
      await client.query('DROP TABLE test_temp')
    } catch (tempError) {
      console.log('⚠️  临时表测试失败:', tempError.message)
    }
    
    await client.end()
    return true
    
  } catch (error) {
    const duration = Date.now() - startTime
    console.log(`❌ SM3 用户连接失败 (${duration}ms)`)
    console.log('错误详情:', error.message)
    
    // 提供具体的错误分析
    if (error.message.includes('authentication')) {
      console.log('💡 认证失败可能的原因:')
      console.log('   1. 用户密码不正确')
      console.log('   2. pg_hba.conf 中未配置 sm3 认证')
      console.log('   3. 用户不存在或被禁用')
      console.log('   4. SM3 认证方法不被客户端支持')
    } else if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.log('💡 数据库不存在:')
      console.log('   1. 确认数据库名称是否正确: seatom-dev')
      console.log('   2. 检查数据库是否已创建')
    } else if (error.message.includes('ECONNREFUSED')) {
      console.log('💡 连接被拒绝，请检查:')
      console.log('   1. 数据库服务是否启动')
      console.log('   2. 主机地址和端口是否正确')
      console.log('   3. 防火墙设置')
    }
    
    return false
  }
}

async function main() {
  const success = await testSM3Connection()
  
  if (success) {
    console.log('\n🎉 SM3 用户连接测试成功!')
    console.log('用户 highdb_user 可以正常访问 seatom-dev 数据库中的所有用户表。')
  } else {
    console.log('\n❌ SM3 用户连接测试失败!')
    console.log('请检查用户创建、权限设置和 pg_hba.conf 配置。')
    process.exit(1)
  }
}

main().catch(console.error)
