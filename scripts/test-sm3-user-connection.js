/**
 * 测试使用 SM3 加密用户连接瀚高数据库
 * 使用方法: node test-sm3-user-connection.js
 */

const pg = require('pg')

const host = 'localhost'
const port = 5866
const username = 'highdb_user'
const password = 'Wufei123!'
const database = 'seatom_dev'

console.log('🔍 测试 SM3 用户连接瀚高数据库...')
console.log('📋 连接参数:')
console.log(`   主机: ${host}`)
console.log(`   端口: ${port}`)
console.log(`   用户: ${username}`)
console.log(`   数据库: ${database}`)
console.log(`   加密方式: SM3`)
console.log('=' * 60)

// SM3 用户连接配置
const sm3Config = {
  host,
  port: parseInt(port),
  user: username,
  password,
  database,
  ssl: false,
  connectionTimeoutMillis: 30000,
  idleTimeoutMillis: 30000,
  application_name: 'seatom-sm3-test'
}

async function testSM3Connection() {
  console.log('🔐 测试 SM3 用户连接...')
  const startTime = Date.now()
  
  try {
    const client = new pg.Client(sm3Config)
    
    await client.connect()
    console.log(`✅ SM3 用户连接成功! (${Date.now() - startTime}ms)`)
    
    // 测试基本信息查询
    const basicInfo = await client.query(`
      SELECT 
        version() as version,
        current_user as current_user,
        current_database() as current_database,
        current_schema() as current_schema
    `)
    
    console.log('📊 数据库信息:')
    console.log(`   版本: ${basicInfo.rows[0].version.substring(0, 50)}...`)
    console.log(`   当前用户: ${basicInfo.rows[0].current_user}`)
    console.log(`   当前数据库: ${basicInfo.rows[0].current_database}`)
    console.log(`   当前模式: ${basicInfo.rows[0].current_schema}`)
    
    await client.end()
    return true
    
  } catch (error) {
    const duration = Date.now() - startTime
    console.log(`❌ SM3 用户连接失败 (${duration}ms)`)
    console.log('错误详情:', error.message)
    
    if (error.message.includes('authentication')) {
      console.log('💡 认证失败可能的原因:')
      console.log('   1. 用户密码不正确')
      console.log('   2. pg_hba.conf 中未配置 sm3 认证')
      console.log('   3. 用户不存在或被禁用')
      console.log('   4. SM3 认证方法不被客户端支持')
    }
    
    return false
  }
}

async function main() {
  const success = await testSM3Connection()
  
  if (success) {
    console.log('\n🎉 SM3 用户连接测试成功!')
    console.log('用户 highdb_user 可以正常访问 seatom_dev 数据库。')
  } else {
    console.log('\n❌ SM3 用户连接测试失败!')
    console.log('请检查用户创建、权限设置和 pg_hba.conf 配置。')
    process.exit(1)
  }
}

main().catch(console.error)
